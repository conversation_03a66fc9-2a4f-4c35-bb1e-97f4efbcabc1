"""
Serializers for Arena Doviz Reports app.
Handles report generation, templates, and analytics data serialization.
"""

from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from datetime import datetime, timedelta
from .models import ReportTemplate, GeneratedReport, ReportSchedule
from apps.accounts.serializers import UserListSerializer
import logging

logger = logging.getLogger(__name__)


class ReportTemplateSerializer(serializers.ModelSerializer):
    """Serializer for ReportTemplate model."""
    
    report_type_display = serializers.CharField(source='get_report_type_display', read_only=True)
    can_be_used = serializers.SerializerMethodField()
    default_parameters = serializers.SerializerMethodField()
    required_parameters = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'report_type', 'report_type_display', 'description',
            'template_config', 'layout_config', 'is_public', 'allowed_roles',
            'is_active', 'sort_order', 'can_be_used', 'default_parameters',
            'required_parameters', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_can_be_used(self, obj):
        """Check if current user can use this template."""
        request = self.context.get('request')
        if not request or not request.user:
            return False
        return obj.can_be_used_by_role(request.user.role)
    
    def get_default_parameters(self, obj):
        """Get default parameters for the template."""
        return obj.get_default_parameters()
    
    def get_required_parameters(self, obj):
        """Get required parameters for the template."""
        return obj.get_required_parameters()


class GeneratedReportSerializer(serializers.ModelSerializer):
    """Serializer for GeneratedReport model."""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    template_type = serializers.CharField(source='template.report_type', read_only=True)
    generated_by_name = serializers.CharField(source='generated_by.get_display_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    format_display = serializers.CharField(source='get_format_display', read_only=True)
    file_size_formatted = serializers.CharField(source='get_file_size_formatted', read_only=True)
    generation_duration = serializers.SerializerMethodField()
    is_downloadable = serializers.SerializerMethodField()
    
    class Meta:
        model = GeneratedReport
        fields = [
            'id', 'template', 'template_name', 'template_type', 'generated_by',
            'generated_by_name', 'title', 'parameters', 'status', 'status_display',
            'format', 'format_display', 'file_path', 'file_size', 'file_size_formatted',
            'generation_started_at', 'generation_completed_at', 'expires_at',
            'error_message', 'record_count', 'download_count', 'last_downloaded_at',
            'generation_duration', 'is_downloadable', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'file_path', 'file_size', 'generation_started_at',
            'generation_completed_at', 'error_message', 'record_count',
            'download_count', 'last_downloaded_at', 'created_at', 'updated_at'
        ]
    
    def get_generation_duration(self, obj):
        """Get formatted generation duration."""
        duration = obj.get_generation_duration()
        if duration:
            total_seconds = int(duration.total_seconds())
            minutes, seconds = divmod(total_seconds, 60)
            return f"{minutes}m {seconds}s"
        return None
    
    def get_is_downloadable(self, obj):
        """Check if report is downloadable."""
        return obj.is_completed() and not obj.is_expired()


class ReportGenerationRequestSerializer(serializers.Serializer):
    """Serializer for report generation requests."""
    
    template_id = serializers.UUIDField()
    title = serializers.CharField(max_length=200, required=False)
    format = serializers.ChoiceField(
        choices=GeneratedReport.Format.choices,
        default=GeneratedReport.Format.PDF
    )
    parameters = serializers.JSONField(default=dict)
    
    def validate_template_id(self, value):
        """Validate template exists and user can access it."""
        try:
            template = ReportTemplate.objects.get(id=value, is_active=True, is_deleted=False)
        except ReportTemplate.DoesNotExist:
            raise serializers.ValidationError("Report template not found or inactive")
        
        # Check if user can use this template
        request = self.context.get('request')
        if request and request.user:
            if not template.can_be_used_by_role(request.user.role):
                raise serializers.ValidationError("You don't have permission to use this template")
        
        return value
    
    def validate(self, data):
        """Validate parameters against template requirements."""
        template = ReportTemplate.objects.get(id=data['template_id'])
        
        try:
            template.validate_parameters(data['parameters'])
        except ValueError as e:
            raise serializers.ValidationError({'parameters': str(e)})
        
        # Set default title if not provided
        if not data.get('title'):
            data['title'] = f"{template.name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        return data


class CustomerStatementRequestSerializer(serializers.Serializer):
    """Serializer for customer statement generation requests."""
    
    customer_id = serializers.UUIDField()
    date_from = serializers.DateField()
    date_to = serializers.DateField()
    location_id = serializers.UUIDField(required=False)
    currency_code = serializers.CharField(max_length=3, required=False)
    format = serializers.ChoiceField(
        choices=GeneratedReport.Format.choices,
        default=GeneratedReport.Format.PDF
    )
    include_balance_history = serializers.BooleanField(default=True)
    include_transaction_details = serializers.BooleanField(default=True)
    
    def validate(self, data):
        """Validate date range and other parameters."""
        if data['date_from'] > data['date_to']:
            raise serializers.ValidationError("Date from must be before date to")
        
        # Check if date range is not too large (max 1 year)
        if (data['date_to'] - data['date_from']).days > 365:
            raise serializers.ValidationError("Date range cannot exceed 1 year")
        
        return data


class ReportScheduleSerializer(serializers.ModelSerializer):
    """Serializer for ReportSchedule model."""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    frequency_display = serializers.CharField(source='get_frequency_display', read_only=True)
    format_display = serializers.CharField(source='get_format_display', read_only=True)
    success_rate = serializers.SerializerMethodField()
    
    class Meta:
        model = ReportSchedule
        fields = [
            'id', 'template', 'template_name', 'name', 'description',
            'frequency', 'frequency_display', 'parameters', 'format',
            'format_display', 'email_recipients', 'next_run_at',
            'last_run_at', 'is_active', 'run_count', 'success_count',
            'failure_count', 'success_rate', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'last_run_at', 'run_count', 'success_count',
            'failure_count', 'created_at', 'updated_at'
        ]
    
    def get_success_rate(self, obj):
        """Get formatted success rate."""
        return f"{obj.get_success_rate():.1f}%"


class ReportStatsSerializer(serializers.Serializer):
    """Serializer for report statistics."""
    
    total_templates = serializers.IntegerField()
    active_templates = serializers.IntegerField()
    total_generated_reports = serializers.IntegerField()
    completed_reports = serializers.IntegerField()
    failed_reports = serializers.IntegerField()
    reports_generated_today = serializers.IntegerField()
    reports_by_type = serializers.DictField()
    reports_by_format = serializers.DictField()
    active_schedules = serializers.IntegerField()
    total_downloads = serializers.IntegerField()
    average_generation_time = serializers.CharField()


class BalanceReportSerializer(serializers.Serializer):
    """Serializer for balance report data."""
    
    report_date = serializers.DateTimeField()
    customer_balances = serializers.ListField()
    company_balances = serializers.ListField()
    total_balances_by_currency = serializers.DictField()
    locations = serializers.ListField()
    currencies = serializers.ListField()


class TransactionReportSerializer(serializers.Serializer):
    """Serializer for transaction report data."""
    
    report_date = serializers.DateTimeField()
    date_from = serializers.DateField()
    date_to = serializers.DateField()
    transactions = serializers.ListField()
    summary = serializers.DictField()
    totals_by_currency = serializers.DictField()
    totals_by_location = serializers.DictField()
    profit_analysis = serializers.DictField()
