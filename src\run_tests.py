#!/usr/bin/env python
"""
Arena Doviz Test Runner
Comprehensive test execution script with coverage and reporting.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the src directory to Python path
BASE_DIR = Path(__file__).resolve().parent
sys.path.insert(0, str(BASE_DIR))

# Set Django settings module for tests
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.test')

import django
from django.conf import settings
from django.test.utils import get_runner


class TestRunner:
    """Enhanced test runner with coverage and reporting capabilities."""
    
    def __init__(self):
        self.base_dir = BASE_DIR
        self.test_results_dir = self.base_dir / 'test_results'
        self.test_results_dir.mkdir(exist_ok=True)
    
    def run_unit_tests(self, apps=None, verbosity=2, keepdb=False, parallel=True):
        """Run unit tests for specified apps or all apps."""
        print("🧪 Running Unit Tests...")
        
        # Setup Django
        django.setup()
        
        # Get test runner
        TestRunnerClass = get_runner(settings)
        test_runner = TestRunnerClass(
            verbosity=verbosity,
            interactive=False,
            keepdb=keepdb,
            parallel=parallel if not keepdb else False
        )
        
        # Determine test labels
        if apps:
            test_labels = [f'tests.test_{app}' for app in apps]
        else:
            test_labels = ['tests']
        
        # Run tests
        failures = test_runner.run_tests(test_labels)
        
        if failures:
            print(f"❌ {failures} test(s) failed")
            return False
        else:
            print("✅ All unit tests passed!")
            return True
    
    def run_with_coverage(self, apps=None, html_report=True, xml_report=True):
        """Run tests with coverage analysis."""
        print("📊 Running Tests with Coverage Analysis...")
        
        try:
            import coverage
        except ImportError:
            print("❌ Coverage.py not installed. Install with: pip install coverage")
            return False
        
        # Initialize coverage
        cov = coverage.Coverage(
            source=['apps'],
            omit=[
                '*/migrations/*',
                '*/tests/*',
                '*/venv/*',
                '*/virtualenv/*',
                'manage.py',
                'config/wsgi.py',
                'config/asgi.py',
            ]
        )
        
        cov.start()
        
        # Run tests
        success = self.run_unit_tests(apps=apps, parallel=False)  # Disable parallel for coverage
        
        cov.stop()
        cov.save()
        
        # Generate reports
        print("\n📈 Coverage Report:")
        cov.report()
        
        if html_report:
            html_dir = self.test_results_dir / 'htmlcov'
            cov.html_report(directory=str(html_dir))
            print(f"📄 HTML coverage report: {html_dir}/index.html")
        
        if xml_report:
            xml_file = self.test_results_dir / 'coverage.xml'
            cov.xml_report(outfile=str(xml_file))
            print(f"📄 XML coverage report: {xml_file}")
        
        return success
    
    def run_performance_tests(self):
        """Run performance tests."""
        print("⚡ Running Performance Tests...")
        
        os.environ['PERFORMANCE_TEST'] = 'True'
        
        # Run specific performance test modules
        performance_tests = [
            'tests.test_performance',
        ]
        
        success = True
        for test_module in performance_tests:
            try:
                result = subprocess.run([
                    sys.executable, 'manage.py', 'test', test_module,
                    '--settings=config.settings.test',
                    '--verbosity=2'
                ], cwd=self.base_dir, check=True)
            except subprocess.CalledProcessError:
                success = False
                print(f"❌ Performance test failed: {test_module}")
        
        return success
    
    def run_integration_tests(self):
        """Run integration tests with real database."""
        print("🔗 Running Integration Tests...")
        
        os.environ['INTEGRATION_TEST'] = 'True'
        
        # Run integration tests
        integration_tests = [
            'tests.test_integration',
        ]
        
        success = True
        for test_module in integration_tests:
            try:
                result = subprocess.run([
                    sys.executable, 'manage.py', 'test', test_module,
                    '--settings=config.settings.test',
                    '--verbosity=2',
                    '--keepdb'
                ], cwd=self.base_dir, check=True)
            except subprocess.CalledProcessError:
                success = False
                print(f"❌ Integration test failed: {test_module}")
        
        return success
    
    def run_selenium_tests(self):
        """Run Selenium UI tests."""
        print("🌐 Running Selenium UI Tests...")
        
        os.environ['SELENIUM_TEST'] = 'True'
        
        try:
            result = subprocess.run([
                sys.executable, 'manage.py', 'test', 'tests.test_selenium',
                '--settings=config.settings.test',
                '--verbosity=2'
            ], cwd=self.base_dir, check=True)
            print("✅ Selenium tests passed!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Selenium tests failed!")
            return False
    
    def run_security_tests(self):
        """Run security tests."""
        print("🔒 Running Security Tests...")
        
        security_tests = [
            'tests.test_security',
        ]
        
        success = True
        for test_module in security_tests:
            try:
                result = subprocess.run([
                    sys.executable, 'manage.py', 'test', test_module,
                    '--settings=config.settings.test',
                    '--verbosity=2'
                ], cwd=self.base_dir, check=True)
            except subprocess.CalledProcessError:
                success = False
                print(f"❌ Security test failed: {test_module}")
        
        return success
    
    def run_all_tests(self, coverage=True):
        """Run all test suites."""
        print("🚀 Running Complete Test Suite...")
        
        results = {}
        
        # Unit tests with coverage
        if coverage:
            results['unit_with_coverage'] = self.run_with_coverage()
        else:
            results['unit'] = self.run_unit_tests()
        
        # Integration tests
        results['integration'] = self.run_integration_tests()
        
        # Security tests
        results['security'] = self.run_security_tests()
        
        # Performance tests
        results['performance'] = self.run_performance_tests()
        
        # Selenium tests (optional)
        if os.environ.get('RUN_SELENIUM', 'False').lower() == 'true':
            results['selenium'] = self.run_selenium_tests()
        
        # Summary
        print("\n" + "="*50)
        print("📋 Test Results Summary:")
        print("="*50)
        
        all_passed = True
        for test_type, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_type.upper()}: {status}")
            if not passed:
                all_passed = False
        
        print("="*50)
        if all_passed:
            print("🎉 All tests passed successfully!")
        else:
            print("💥 Some tests failed. Check the output above.")
        
        return all_passed
    
    def generate_test_report(self):
        """Generate comprehensive test report."""
        print("📊 Generating Test Report...")
        
        report_file = self.test_results_dir / 'test_report.html'
        
        # This would generate an HTML report combining all test results
        # For now, just create a simple summary
        with open(report_file, 'w') as f:
            f.write("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Arena Doviz Test Report</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                    .section { margin: 20px 0; }
                    .passed { color: green; }
                    .failed { color: red; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Arena Doviz Test Report</h1>
                    <p>Generated on: {timestamp}</p>
                </div>
                
                <div class="section">
                    <h2>Test Coverage</h2>
                    <p>See coverage report in htmlcov/index.html</p>
                </div>
                
                <div class="section">
                    <h2>Test Results</h2>
                    <p>Detailed test results are available in the console output.</p>
                </div>
            </body>
            </html>
            """.format(timestamp=django.utils.timezone.now()))
        
        print(f"📄 Test report generated: {report_file}")


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description='Arena Doviz Test Runner')
    parser.add_argument('--apps', nargs='+', help='Specific apps to test')
    parser.add_argument('--coverage', action='store_true', help='Run with coverage')
    parser.add_argument('--performance', action='store_true', help='Run performance tests')
    parser.add_argument('--integration', action='store_true', help='Run integration tests')
    parser.add_argument('--selenium', action='store_true', help='Run Selenium tests')
    parser.add_argument('--security', action='store_true', help='Run security tests')
    parser.add_argument('--all', action='store_true', help='Run all test suites')
    parser.add_argument('--keepdb', action='store_true', help='Keep test database')
    parser.add_argument('--parallel', action='store_true', default=True, help='Run tests in parallel')
    parser.add_argument('--report', action='store_true', help='Generate test report')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.all:
        success = runner.run_all_tests(coverage=args.coverage)
    elif args.coverage:
        success = runner.run_with_coverage(apps=args.apps)
    elif args.performance:
        success = runner.run_performance_tests()
    elif args.integration:
        success = runner.run_integration_tests()
    elif args.selenium:
        success = runner.run_selenium_tests()
    elif args.security:
        success = runner.run_security_tests()
    else:
        success = runner.run_unit_tests(
            apps=args.apps,
            keepdb=args.keepdb,
            parallel=args.parallel
        )
    
    if args.report:
        runner.generate_test_report()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
