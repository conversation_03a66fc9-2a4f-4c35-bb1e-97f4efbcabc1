"""
API views for Arena Doviz Reports app.
Handles report generation, templates, and analytics.
"""

from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Sum, Q
from django.db import transaction
from django.utils import timezone
from django.http import HttpResponse, Http404
from datetime import timedel<PERSON>, datetime
from .models import ReportTemplate, GeneratedReport, ReportSchedule
from .serializers import (
    ReportTemplateSerializer, GeneratedReportSerializer, ReportGenerationRequestSerializer,
    CustomerStatementRequestSerializer, ReportScheduleSerializer, ReportStatsSerializer,
    BalanceReportSerializer, TransactionReportSerializer
)
from apps.core.utils import log_user_action, get_client_ip, calculate_customer_balance, calculate_company_balance
import logging
import os

logger = logging.getLogger(__name__)


class ReportTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for ReportTemplate management."""
    
    queryset = ReportTemplate.objects.filter(is_deleted=False)
    serializer_class = ReportTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'report_type', 'sort_order']
    ordering = ['sort_order', 'name']
    
    def get_queryset(self):
        """Filter templates based on user permissions."""
        user = self.request.user
        queryset = self.queryset.filter(is_active=True)
        
        # Filter by report type if specified
        report_type = self.request.query_params.get('report_type')
        if report_type:
            queryset = queryset.filter(report_type=report_type)
        
        # Filter by user's role permissions
        if not user.can_manage_users():
            queryset = queryset.filter(
                Q(is_public=True) | Q(allowed_roles__contains=user.role)
            )
        
        return queryset


class GeneratedReportViewSet(viewsets.ModelViewSet):
    """ViewSet for GeneratedReport management."""
    
    queryset = GeneratedReport.objects.filter(is_deleted=False)
    serializer_class = GeneratedReportSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'template__name']
    ordering_fields = ['title', 'generation_started_at', 'status']
    ordering = ['-generation_started_at']
    
    def get_queryset(self):
        """Filter reports based on user permissions."""
        user = self.request.user
        queryset = self.queryset
        
        # Non-admin users can only see their own reports
        if not user.can_manage_users():
            queryset = queryset.filter(generated_by=user)
        
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by template type
        template_type = self.request.query_params.get('template_type')
        if template_type:
            queryset = queryset.filter(template__report_type=template_type)
        
        # Filter by date range
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(generation_started_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(generation_started_at__date__lte=date_to)
        
        return queryset
    
    @action(detail=False, methods=['post'])
    def generate(self, request):
        """Generate a new report."""
        serializer = ReportGenerationRequestSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            template = ReportTemplate.objects.get(id=serializer.validated_data['template_id'])
            
            # Create the generated report record
            generated_report = GeneratedReport.objects.create(
                template=template,
                generated_by=request.user,
                title=serializer.validated_data['title'],
                parameters=serializer.validated_data['parameters'],
                format=serializer.validated_data['format'],
                status=GeneratedReport.Status.GENERATING
            )
            
            # Log the report generation request
            log_user_action(
                user=request.user,
                action='generate_report',
                model_name='GeneratedReport',
                object_id=str(generated_report.id),
                object_repr=str(generated_report),
                ip_address=get_client_ip(request),
                additional_data={
                    'template': template.name,
                    'format': serializer.validated_data['format'],
                    'parameters': serializer.validated_data['parameters']
                }
            )
            
            # TODO: Implement actual report generation logic here
            # For now, we'll mark it as completed immediately
            try:
                # This would be replaced with actual report generation
                self._generate_report_content(generated_report)
                
                logger.info(f"Report generation started: {generated_report.title} by {request.user}")
                
                return Response({
                    'message': 'Report generation started successfully',
                    'report': GeneratedReportSerializer(generated_report).data
                })
                
            except Exception as e:
                generated_report.mark_failed(str(e))
                logger.error(f"Report generation failed: {e}")
                
                return Response(
                    {'error': f'Report generation failed: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def generate_customer_statement(self, request):
        """Generate a customer statement report."""
        serializer = CustomerStatementRequestSerializer(data=request.data)
        
        if serializer.is_valid():
            # Get customer statement template
            try:
                template = ReportTemplate.objects.get(
                    report_type=ReportTemplate.ReportType.CUSTOMER_STATEMENT,
                    is_active=True,
                    is_deleted=False
                )
            except ReportTemplate.DoesNotExist:
                return Response(
                    {'error': 'Customer statement template not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Create the generated report record
            generated_report = GeneratedReport.objects.create(
                template=template,
                generated_by=request.user,
                title=f"Customer Statement - {serializer.validated_data['date_from']} to {serializer.validated_data['date_to']}",
                parameters=serializer.validated_data,
                format=serializer.validated_data['format'],
                status=GeneratedReport.Status.GENERATING
            )
            
            try:
                # Generate customer statement
                self._generate_customer_statement(generated_report, serializer.validated_data)
                
                logger.info(f"Customer statement generated: {generated_report.title} by {request.user}")
                
                return Response({
                    'message': 'Customer statement generated successfully',
                    'report': GeneratedReportSerializer(generated_report).data
                })
                
            except Exception as e:
                generated_report.mark_failed(str(e))
                logger.error(f"Customer statement generation failed: {e}")
                
                return Response(
                    {'error': f'Customer statement generation failed: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download a generated report."""
        generated_report = self.get_object()
        
        # Check if report is downloadable
        if not generated_report.is_completed():
            return Response(
                {'error': 'Report is not ready for download'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if generated_report.is_expired():
            return Response(
                {'error': 'Report has expired'},
                status=status.HTTP_410_GONE
            )
        
        # Check if file exists
        if not generated_report.file_path or not os.path.exists(generated_report.file_path):
            return Response(
                {'error': 'Report file not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        try:
            # Read the file
            with open(generated_report.file_path, 'rb') as f:
                file_content = f.read()
            
            # Determine content type based on format
            content_types = {
                GeneratedReport.Format.PDF: 'application/pdf',
                GeneratedReport.Format.EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                GeneratedReport.Format.CSV: 'text/csv',
                GeneratedReport.Format.JSON: 'application/json',
                GeneratedReport.Format.HTML: 'text/html',
            }
            
            content_type = content_types.get(generated_report.format, 'application/octet-stream')
            
            # Create response
            response = HttpResponse(file_content, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{generated_report.title}.{generated_report.format}"'
            
            # Mark as downloaded
            generated_report.mark_downloaded()
            
            # Log the download
            log_user_action(
                user=request.user,
                action='download_report',
                model_name='GeneratedReport',
                object_id=str(generated_report.id),
                object_repr=str(generated_report),
                ip_address=get_client_ip(request),
                additional_data={
                    'title': generated_report.title,
                    'format': generated_report.format
                }
            )
            
            logger.info(f"Report downloaded: {generated_report.title} by {request.user}")
            
            return response
            
        except Exception as e:
            logger.error(f"Error downloading report {generated_report.id}: {e}")
            return Response(
                {'error': 'Error downloading report'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _generate_report_content(self, generated_report):
        """Generate the actual report content (placeholder implementation)."""
        # This is a placeholder - in a real implementation, you would:
        # 1. Execute the template's SQL query or use the template configuration
        # 2. Format the data according to the template's layout configuration
        # 3. Generate the file in the requested format (PDF, Excel, etc.)
        # 4. Save the file and update the generated_report record
        
        # For now, just mark as completed with dummy data
        import tempfile
        import json
        
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{generated_report.format}') as temp_file:
            if generated_report.format == GeneratedReport.Format.JSON:
                dummy_data = {
                    'report_title': generated_report.title,
                    'generated_at': timezone.now().isoformat(),
                    'parameters': generated_report.parameters,
                    'data': 'This is a placeholder report'
                }
                temp_file.write(json.dumps(dummy_data, indent=2).encode())
            else:
                temp_file.write(b'This is a placeholder report file')
            
            file_path = temp_file.name
        
        # Mark as completed
        file_size = os.path.getsize(file_path)
        generated_report.mark_completed(file_path, file_size, 1)
    
    def _generate_customer_statement(self, generated_report, parameters):
        """Generate customer statement content."""
        # This would implement the actual customer statement generation
        # For now, use the placeholder implementation
        self._generate_report_content(generated_report)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get report statistics."""
        # Calculate basic statistics
        total_templates = ReportTemplate.objects.filter(is_deleted=False).count()
        active_templates = ReportTemplate.objects.filter(is_active=True, is_deleted=False).count()

        total_generated_reports = GeneratedReport.objects.filter(is_deleted=False).count()
        completed_reports = GeneratedReport.objects.filter(
            status=GeneratedReport.Status.COMPLETED,
            is_deleted=False
        ).count()
        failed_reports = GeneratedReport.objects.filter(
            status=GeneratedReport.Status.FAILED,
            is_deleted=False
        ).count()

        # Today's reports
        today = timezone.now().date()
        reports_generated_today = GeneratedReport.objects.filter(
            generation_started_at__date=today,
            is_deleted=False
        ).count()

        # Reports by type
        reports_by_type = dict(
            GeneratedReport.objects.filter(is_deleted=False)
            .values('template__report_type')
            .annotate(count=Count('id'))
            .values_list('template__report_type', 'count')
        )

        # Reports by format
        reports_by_format = dict(
            GeneratedReport.objects.filter(is_deleted=False)
            .values('format')
            .annotate(count=Count('id'))
            .values_list('format', 'count')
        )

        # Active schedules
        active_schedules = ReportSchedule.objects.filter(
            is_active=True,
            is_deleted=False
        ).count()

        # Total downloads
        total_downloads = GeneratedReport.objects.filter(
            is_deleted=False
        ).aggregate(
            total=Sum('download_count')
        )['total'] or 0

        # Average generation time (placeholder)
        average_generation_time = "2m 30s"  # This would be calculated from actual data

        stats_data = {
            'total_templates': total_templates,
            'active_templates': active_templates,
            'total_generated_reports': total_generated_reports,
            'completed_reports': completed_reports,
            'failed_reports': failed_reports,
            'reports_generated_today': reports_generated_today,
            'reports_by_type': reports_by_type,
            'reports_by_format': reports_by_format,
            'active_schedules': active_schedules,
            'total_downloads': total_downloads,
            'average_generation_time': average_generation_time
        }

        serializer = ReportStatsSerializer(stats_data)
        return Response(serializer.data)


class ReportScheduleViewSet(viewsets.ModelViewSet):
    """ViewSet for ReportSchedule management."""

    queryset = ReportSchedule.objects.filter(is_deleted=False)
    serializer_class = ReportScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['next_run_at']

    def get_queryset(self):
        """Filter schedules based on user permissions."""
        user = self.request.user
        queryset = self.queryset

        # Only admins and accountants can manage schedules
        if not user.can_manage_users() and not user.is_accountant():
            return ReportSchedule.objects.none()

        # Filter by active status
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def due_schedules(self, request):
        """Get schedules that are due to run."""
        due_schedules = ReportSchedule.get_due_schedules()
        serializer = self.get_serializer(due_schedules, many=True)
        return Response(serializer.data)


class ReportDataViewSet(viewsets.ViewSet):
    """ViewSet for report data generation without file creation."""

    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def balance_report(self, request):
        """Generate balance report data."""
        location_id = request.query_params.get('location_id')
        currency_code = request.query_params.get('currency_code')

        try:
            # Get customer balances
            from apps.customers.models import Customer
            customers = Customer.objects.filter(is_deleted=False, status='active')

            customer_balances = []
            for customer in customers:
                balances = calculate_customer_balance(customer.id, currency_code, location_id)
                if balances:
                    customer_balances.append({
                        'customer': customer.get_display_name(),
                        'customer_id': str(customer.id),
                        'balances': balances
                    })

            # Get company balances
            company_balances = calculate_company_balance(currency_code, location_id)

            # Get locations and currencies for reference
            from apps.locations.models import Location
            from apps.currencies.models import Currency

            locations = [
                {'id': str(loc.id), 'name': loc.name}
                for loc in Location.objects.filter(is_active=True, is_deleted=False)
            ]

            currencies = [
                {'id': str(curr.id), 'code': curr.code, 'symbol': curr.symbol}
                for curr in Currency.objects.filter(is_active=True, is_deleted=False)
            ]

            # Calculate totals by currency
            total_balances_by_currency = {}
            # This would be implemented based on the actual balance calculation logic

            report_data = {
                'report_date': timezone.now(),
                'customer_balances': customer_balances,
                'company_balances': company_balances,
                'total_balances_by_currency': total_balances_by_currency,
                'locations': locations,
                'currencies': currencies
            }

            serializer = BalanceReportSerializer(report_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error generating balance report: {e}")
            return Response(
                {'error': f'Error generating balance report: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def transaction_report(self, request):
        """Generate transaction report data."""
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        location_id = request.query_params.get('location_id')
        currency_code = request.query_params.get('currency_code')

        if not date_from or not date_to:
            return Response(
                {'error': 'date_from and date_to parameters are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from apps.transactions.models import Transaction
            from datetime import datetime

            # Parse dates
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()

            # Get transactions
            transactions = Transaction.objects.filter(
                created_at__date__gte=date_from_obj,
                created_at__date__lte=date_to_obj,
                is_deleted=False
            )

            # Apply filters
            if location_id:
                transactions = transactions.filter(location_id=location_id)
            if currency_code:
                transactions = transactions.filter(
                    Q(from_currency__code=currency_code) | Q(to_currency__code=currency_code)
                )

            # Serialize transaction data
            from apps.transactions.serializers import TransactionListSerializer
            transaction_data = TransactionListSerializer(transactions, many=True).data

            # Calculate summary statistics
            summary = {
                'total_transactions': transactions.count(),
                'completed_transactions': transactions.filter(status='completed').count(),
                'total_volume': transactions.filter(status='completed').aggregate(
                    total=Sum('from_amount')
                )['total'] or 0,
                'total_commission': transactions.filter(status='completed').aggregate(
                    total=Sum('commission_amount')
                )['total'] or 0
            }

            report_data = {
                'report_date': timezone.now(),
                'date_from': date_from_obj,
                'date_to': date_to_obj,
                'transactions': transaction_data,
                'summary': summary,
                'totals_by_currency': {},  # Would be calculated
                'totals_by_location': {},  # Would be calculated
                'profit_analysis': {}  # Would be calculated
            }

            serializer = TransactionReportSerializer(report_data)
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"Error generating transaction report: {e}")
            return Response(
                {'error': f'Error generating transaction report: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
