"""
Core models for Arena Doviz Exchange Accounting System.
Provides base models and common functionality.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
import uuid
import logging

logger = logging.getLogger(__name__)


class TimeStampedModel(models.Model):
    """
    Abstract base model that provides self-updating created and modified fields.
    """
    created_at = models.DateTimeField(
        _('Created at'),
        auto_now_add=True,
        help_text=_('Date and time when the record was created')
    )
    updated_at = models.DateTimeField(
        _('Updated at'),
        auto_now=True,
        help_text=_('Date and time when the record was last updated')
    )
    
    class Meta:
        abstract = True
        ordering = ['-created_at']


class UUIDModel(models.Model):
    """
    Abstract base model that provides a UUID primary key.
    """
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_('Unique identifier for this record')
    )
    
    class Meta:
        abstract = True


class AuditModel(TimeStampedModel):
    """
    Abstract base model that provides audit trail functionality.
    """
    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_created',
        null=True,
        blank=True,
        verbose_name=_('Created by'),
        help_text=_('User who created this record')
    )
    updated_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_updated',
        null=True,
        blank=True,
        verbose_name=_('Updated by'),
        help_text=_('User who last updated this record')
    )
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """
        Override save to automatically set created_by and updated_by fields.
        """
        user = kwargs.pop('user', None)
        
        if user and hasattr(user, 'id'):
            if not self.pk:  # New record
                self.created_by = user
            self.updated_by = user
        
        super().save(*args, **kwargs)
        
        # Log the save operation
        action = 'created' if not self.pk else 'updated'
        logger.info(
            f"{self.__class__.__name__} {action}: {self.pk} by user {user.id if user else 'system'}"
        )


class SoftDeleteModel(models.Model):
    """
    Abstract base model that provides soft delete functionality.
    """
    is_deleted = models.BooleanField(
        _('Is deleted'),
        default=False,
        help_text=_('Whether this record has been soft deleted')
    )
    deleted_at = models.DateTimeField(
        _('Deleted at'),
        null=True,
        blank=True,
        help_text=_('Date and time when the record was deleted')
    )
    deleted_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)s_deleted',
        null=True,
        blank=True,
        verbose_name=_('Deleted by'),
        help_text=_('User who deleted this record')
    )
    
    class Meta:
        abstract = True
    
    def delete(self, user=None, *args, **kwargs):
        """
        Soft delete the record instead of actually deleting it.
        """
        from django.utils import timezone
        
        self.is_deleted = True
        self.deleted_at = timezone.now()
        if user:
            self.deleted_by = user
        
        self.save(update_fields=['is_deleted', 'deleted_at', 'deleted_by'])
        
        logger.info(
            f"{self.__class__.__name__} soft deleted: {self.pk} by user {user.id if user else 'system'}"
        )
    
    def restore(self, user=None):
        """
        Restore a soft deleted record.
        """
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        
        self.save(update_fields=['is_deleted', 'deleted_at', 'deleted_by'])
        
        logger.info(
            f"{self.__class__.__name__} restored: {self.pk} by user {user.id if user else 'system'}"
        )


class BaseModel(UUIDModel, AuditModel, SoftDeleteModel):
    """
    Base model that combines UUID, audit trail, and soft delete functionality.
    """
    
    class Meta:
        abstract = True
    
    def __str__(self):
        """
        Default string representation.
        """
        return f"{self.__class__.__name__} ({self.pk})"


class SystemConfiguration(TimeStampedModel):
    """
    Model for storing system-wide configuration settings.
    """
    key = models.CharField(
        _('Configuration key'),
        max_length=100,
        unique=True,
        help_text=_('Unique key for the configuration setting')
    )
    value = models.TextField(
        _('Configuration value'),
        help_text=_('Value of the configuration setting')
    )
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Description of what this configuration setting does')
    )
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this configuration setting is active')
    )
    
    class Meta:
        verbose_name = _('System Configuration')
        verbose_name_plural = _('System Configurations')
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value[:50]}..."
    
    @classmethod
    def get_value(cls, key, default=None):
        """
        Get a configuration value by key.
        """
        try:
            config = cls.objects.get(key=key, is_active=True)
            return config.value
        except cls.DoesNotExist:
            logger.warning(f"Configuration key '{key}' not found, using default: {default}")
            return default
    
    @classmethod
    def set_value(cls, key, value, description='', user=None):
        """
        Set a configuration value.
        """
        config, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description,
            }
        )
        
        if not created:
            config.value = value
            config.description = description
            config.save()
        
        action = 'created' if created else 'updated'
        logger.info(f"Configuration {action}: {key} = {value} by user {user.id if user else 'system'}")
        
        return config
