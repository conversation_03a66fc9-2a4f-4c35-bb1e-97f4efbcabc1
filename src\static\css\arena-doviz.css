/* Arena Doviz Exchange Accounting System - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* Dashboard Cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

/* Table Enhancements */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: rgba(0, 0, 0, 0.03);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Button Enhancements */
.btn {
    font-weight: 500;
    transition: all 0.15s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Currency Display */
.currency-amount {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.currency-positive {
    color: var(--success-color);
}

.currency-negative {
    color: var(--danger-color);
}

/* Status Indicators */
.status-active {
    color: var(--success-color);
}

.status-inactive {
    color: var(--secondary-color);
}

.status-pending {
    color: var(--warning-color);
}

.status-completed {
    color: var(--success-color);
}

.status-cancelled {
    color: var(--danger-color);
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.125rem solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .card-header .btn,
    footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table th {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Indicators */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000000;
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-currency {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color), #0aa2c0);
}
