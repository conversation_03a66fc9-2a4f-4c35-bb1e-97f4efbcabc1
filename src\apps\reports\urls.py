"""
API URL configuration for Arena Doviz Reports app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ReportTemplateViewSet, GeneratedReportViewSet, ReportScheduleViewSet, ReportDataViewSet

app_name = 'reports'

# API router for reports endpoints
router = DefaultRouter()
router.register(r'templates', ReportTemplateViewSet)
router.register(r'generated', GeneratedReportViewSet)
router.register(r'schedules', ReportScheduleViewSet)
router.register(r'data', ReportDataViewSet, basename='report-data')

urlpatterns = [
    path('', include(router.urls)),
]
