"""
API views for Arena Doviz Accounts app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import login, logout
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedel<PERSON>
from .models import User, UserSession, AuditLog
from .serializers import (
    UserSerializer, UserProfileSerializer, LoginSerializer,
    ChangePasswordSerializer, UserSessionSerializer, AuditLogSerializer,
    UserStatsSerializer
)
from apps.core.utils import log_user_action, get_client_ip, create_success_response, create_error_response
import logging

logger = logging.getLogger(__name__)


class UserViewSet(viewsets.ModelViewSet):
    """ViewSet for User management."""
    
    queryset = User.objects.filter(is_deleted=False)
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on user permissions."""
        user = self.request.user
        
        # Admins can see all users
        if user.can_manage_users():
            return self.queryset
        
        # Other users can only see themselves
        return self.queryset.filter(id=user.id)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action in ['retrieve', 'update', 'partial_update']:
            return UserProfileSerializer
        return UserSerializer
    
    def perform_create(self, serializer):
        """Create user with permission checking and audit logging."""
        # Check if user can manage users
        if not self.request.user.can_manage_users():
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to create users.")

        user = serializer.save()

        log_user_action(
            user=self.request.user,
            action='create',
            model_name='User',
            object_id=str(user.id),
            object_repr=str(user),
            ip_address=get_client_ip(self.request)
        )
    
    def perform_update(self, serializer):
        """Update user with permission checking and audit logging."""
        old_instance = self.get_object()

        # Check if user can manage users (unless updating themselves)
        if old_instance != self.request.user and not self.request.user.can_manage_users():
            from rest_framework.exceptions import PermissionDenied
            raise PermissionDenied("You don't have permission to update other users.")

        user = serializer.save()

        log_user_action(
            user=self.request.user,
            action='update',
            model_name='User',
            object_id=str(user.id),
            object_repr=str(user),
            ip_address=get_client_ip(self.request)
        )
    
    def perform_destroy(self, instance):
        """Soft delete user with audit logging."""
        instance.delete(user=self.request.user)
        
        log_user_action(
            user=self.request.user,
            action='delete',
            model_name='User',
            object_id=str(instance.id),
            object_repr=str(instance),
            ip_address=get_client_ip(self.request)
        )
    
    @action(detail=False, methods=['post'])
    def login(self, request):
        """User login endpoint."""
        serializer = LoginSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Create or get token
            token, created = Token.objects.get_or_create(user=user)
            
            # Login user
            login(request, user)
            
            # Log successful login
            log_user_action(
                user=user,
                action='login',
                ip_address=get_client_ip(request),
                additional_data={'login_method': 'api'}
            )
            
            return Response({
                'token': token.key,
                'user': UserProfileSerializer(user).data,
                'message': 'Login successful'
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def logout(self, request):
        """User logout endpoint."""
        if request.user.is_authenticated:
            # Delete token
            try:
                token = Token.objects.get(user=request.user)
                token.delete()
            except Token.DoesNotExist:
                pass
            
            # Log logout
            log_user_action(
                user=request.user,
                action='logout',
                ip_address=get_client_ip(request),
                additional_data={'logout_method': 'api'}
            )
            
            # Logout user
            logout(request)
            
            return Response({'message': 'Logout successful'})
        
        return Response({'message': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)

    @action(detail=False, methods=['post'])
    def jwt_logout(self, request):
        """JWT logout endpoint - blacklist refresh token."""
        from rest_framework_simplejwt.tokens import RefreshToken

        if not request.user.is_authenticated:
            return Response({'message': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            # Log JWT logout
            log_user_action(
                user=request.user,
                action='jwt_logout',
                ip_address=get_client_ip(request),
                additional_data={'logout_method': 'jwt'}
            )

            return Response({'message': 'JWT logout successful'})

        except Exception as e:
            logger.error(f"JWT logout error for user {request.user.username}: {str(e)}")
            return Response({'message': 'Logout failed'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def profile(self, request):
        """Get current user profile."""
        if request.user.is_authenticated:
            serializer = UserProfileSerializer(request.user)
            return Response(serializer.data)
        
        return Response({'message': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)
    
    @action(detail=False, methods=['put'])
    def update_profile(self, request):
        """Update current user profile."""
        if not request.user.is_authenticated:
            return Response({'message': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)
        
        serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='User',
                object_id=str(request.user.id),
                object_repr=str(request.user),
                ip_address=get_client_ip(request),
                additional_data={'profile_update': True}
            )
            
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def change_password(self, request):
        """Change user password."""
        if not request.user.is_authenticated:
            return Response({'message': 'Not authenticated'}, status=status.HTTP_401_UNAUTHORIZED)
        
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        
        if serializer.is_valid():
            serializer.save()
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='User',
                object_id=str(request.user.id),
                object_repr=str(request.user),
                ip_address=get_client_ip(request),
                additional_data={'password_changed': True}
            )
            
            return Response({'message': 'Password changed successfully'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def stats(self, request):
        """Get user statistics (admin only)."""
        if not request.user.can_manage_users():
            return Response({'message': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
        
        # Calculate statistics
        total_users = User.objects.filter(is_deleted=False).count()
        active_users = User.objects.filter(is_active=True, is_deleted=False).count()
        
        # Users by role
        users_by_role = dict(
            User.objects.filter(is_deleted=False)
            .values('role')
            .annotate(count=Count('id'))
            .values_list('role', 'count')
        )
        
        # Users by location
        users_by_location = dict(
            User.objects.filter(is_deleted=False, location__isnull=False)
            .values('location__name')
            .annotate(count=Count('id'))
            .values_list('location__name', 'count')
        )
        
        # Recent logins (last 24 hours)
        yesterday = timezone.now() - timedelta(days=1)
        recent_logins = User.objects.filter(
            last_login__gte=yesterday,
            is_deleted=False
        ).count()
        
        # Failed login attempts (last 24 hours)
        failed_login_attempts = User.objects.filter(
            failed_login_attempts__gt=0,
            is_deleted=False
        ).count()
        
        stats_data = {
            'total_users': total_users,
            'active_users': active_users,
            'users_by_role': users_by_role,
            'users_by_location': users_by_location,
            'recent_logins': recent_logins,
            'failed_login_attempts': failed_login_attempts
        }
        
        serializer = UserStatsSerializer(stats_data)
        return Response(serializer.data)


class UserSessionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for UserSession management (read-only)."""
    
    queryset = UserSession.objects.all()
    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter sessions based on user permissions."""
        user = self.request.user
        
        # Admins can see all sessions
        if user.can_manage_users():
            return self.queryset
        
        # Other users can only see their own sessions
        return self.queryset.filter(user=user)
    
    @action(detail=True, methods=['post'])
    def end_session(self, request, pk=None):
        """End a user session."""
        session = self.get_object()
        
        # Check permissions
        if not request.user.can_manage_users() and session.user != request.user:
            return Response({'message': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
        
        if session.is_active:
            session.end_session()
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='UserSession',
                object_id=str(session.id),
                object_repr=str(session),
                ip_address=get_client_ip(request),
                additional_data={'session_ended': True}
            )
            
            return Response({'message': 'Session ended successfully'})
        
        return Response({'message': 'Session is already ended'}, status=status.HTTP_400_BAD_REQUEST)


class AuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for AuditLog management (read-only)."""
    
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter audit logs based on user permissions."""
        user = self.request.user
        
        # Only admins and accountants can view audit logs
        if user.can_manage_users() or user.is_accountant():
            return self.queryset.order_by('-created_at')
        
        return AuditLog.objects.none()
    
    def list(self, request, *args, **kwargs):
        """List audit logs with filtering."""
        queryset = self.filter_queryset(self.get_queryset())
        
        # Apply filters
        user_id = request.query_params.get('user_id')
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        action = request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)
        
        model_name = request.query_params.get('model_name')
        if model_name:
            queryset = queryset.filter(model_name=model_name)
        
        # Date range filtering
        date_from = request.query_params.get('date_from')
        date_to = request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
