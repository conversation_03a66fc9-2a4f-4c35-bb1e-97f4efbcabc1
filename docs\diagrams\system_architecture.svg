<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2c3e50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3498db;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="frontendGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="backendGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dbGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8e44ad;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9b59b6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1200" height="800" fill="#f8f9fa"/>

  <!-- Title -->
  <rect x="0" y="0" width="1200" height="60" fill="url(#headerGrad)"/>
  <text x="600" y="35" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="Arial">
    Arena Doviz System Architecture
  </text>

  <!-- Internet/Users Layer -->
  <rect x="50" y="80" width="1100" height="80" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="105" text-anchor="middle" fill="#2c3e50" font-size="16" font-weight="bold" font-family="Arial">
    Internal Users Access
  </text>

  <!-- User Icons -->
  <g transform="translate(150, 115)">
    <circle cx="0" cy="0" r="15" fill="#3498db"/>
    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Admin</text>
  </g>
  <g transform="translate(300, 115)">
    <circle cx="0" cy="0" r="15" fill="#e67e22"/>
    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Accountant</text>
  </g>
  <g transform="translate(450, 115)">
    <circle cx="0" cy="0" r="15" fill="#f39c12"/>
    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Operator</text>
  </g>
  <g transform="translate(600, 115)">
    <circle cx="0" cy="0" r="15" fill="#9b59b6"/>
    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Viewer</text>
  </g>
  <g transform="translate(750, 115)">
    <circle cx="0" cy="0" r="15" fill="#1abc9c"/>
    <text x="0" y="25" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Courier (no login)</text>
  </g>

  <!-- Load Balancer/Nginx -->
  <rect x="450" y="200" width="300" height="60" fill="#34495e" stroke="#2c3e50" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="225" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">
    Nginx Web Server
  </text>
  <text x="600" y="245" text-anchor="middle" fill="white" font-size="12" font-family="Arial">
    Load Balancer + SSL/TLS
  </text>

  <!-- Frontend Layer -->
  <rect x="50" y="300" width="500" height="180" fill="url(#frontendGrad)" stroke="#c0392b" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="300" y="325" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Frontend Layer
  </text>

  <!-- Frontend Components -->
  <rect x="70" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="140" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">HTML5 + CSS3</text>
  <text x="140" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Bootstrap 5</text>

  <rect x="230" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="300" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">JavaScript</text>
  <text x="300" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">jQuery + Chart.js</text>

  <rect x="390" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="460" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Responsive</text>
  <text x="460" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Mobile + Desktop</text>

  <rect x="150" y="410" width="200" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="250" y="430" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">DataTables + Forms</text>
  <text x="250" y="445" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Advanced UI Components</text>

  <!-- Backend Layer -->
  <rect x="650" y="300" width="500" height="180" fill="url(#backendGrad)" stroke="#229954" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="900" y="325" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Backend Layer
  </text>

  <!-- Backend Components -->
  <rect x="670" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="740" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Django 4.2+</text>
  <text x="740" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Python Framework</text>

  <rect x="830" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="900" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">REST API</text>
  <text x="900" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">DRF + JWT Auth</text>

  <rect x="990" y="340" width="140" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="1060" y="360" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Gunicorn</text>
  <text x="1060" y="375" text-anchor="middle" fill="white" font-size="10" font-family="Arial">WSGI Server</text>

  <rect x="750" y="410" width="200" height="50" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="850" y="430" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Business Logic</text>
  <text x="850" y="445" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Transactions + Balances</text>

  <!-- Database Layer -->
  <rect x="200" y="520" width="800" height="180" fill="url(#dbGrad)" stroke="#8e44ad" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="545" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">
    Data Layer
  </text>

  <!-- Database Components -->
  <rect x="220" y="560" width="180" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="310" y="580" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">PostgreSQL 15+</text>
  <text x="310" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Primary Database</text>
  <text x="310" y="610" text-anchor="middle" fill="white" font-size="10" font-family="Arial">ACID Compliance</text>

  <rect x="420" y="560" width="180" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="510" y="580" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Redis Cache</text>
  <text x="510" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Session Store</text>
  <text x="510" y="610" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Application Cache</text>

  <rect x="620" y="560" width="180" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="710" y="580" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">File Storage</text>
  <text x="710" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Documents</text>
  <text x="710" y="610" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Receipts + Photos</text>

  <rect x="820" y="560" width="160" height="60" fill="rgba(255,255,255,0.2)" stroke="white" stroke-width="1" rx="5"/>
  <text x="900" y="580" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Backup System</text>
  <text x="900" y="595" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Daily Backups</text>
  <text x="900" y="610" text-anchor="middle" fill="white" font-size="10" font-family="Arial">AES-256 Encrypted</text>

  <!-- Security Layer -->
  <rect x="50" y="730" width="1100" height="50" fill="#e74c3c" stroke="#c0392b" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="600" y="750" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">
    Security Layer: HTTPS/TLS + JWT Authentication + AES-256 Encryption + CSRF Protection + Audit Logging
  </text>
  <!-- WhatsApp Desktop note -->
  <text x="600" y="705" text-anchor="middle" fill="#2c3e50" font-size="12" font-family="Arial">WhatsApp Desktop: open → preview → approve → send</text>

  <!-- Arrows -->
  <!-- Users to Nginx -->
  <path d="M 600 160 L 600 200" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Nginx to Frontend/Backend -->
  <path d="M 550 260 L 300 300" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 650 260 L 900 300" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Frontend to Backend -->
  <path d="M 550 390 L 650 390" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Backend to Database -->
  <path d="M 900 480 L 600 520" stroke="#2c3e50" stroke-width="3" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- Data Flow Labels -->
  <text x="520" y="185" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">HTTPS</text>
  <text x="425" y="285" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Static Files</text>
  <text x="775" y="285" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">API Requests</text>
  <text x="600" y="375" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">AJAX/REST</text>
  <text x="750" y="505" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">SQL Queries</text>
</svg>
