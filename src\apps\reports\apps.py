"""
Reports app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class ReportsConfig(AppConfig):
    """Configuration for the reports app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.reports'
    verbose_name = 'Arena Doviz Reports'
    
    def ready(self):
        """
        Initialize the reports app when Django starts.
        """
        logger.info("Arena Doviz Reports app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Reports app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import reports app signals: {e}")
        
        # Initialize reporting services
        self._initialize_reporting_services()
    
    def _initialize_reporting_services(self):
        """Initialize reporting and analytics services."""
        logger.debug("Initializing reporting services...")
        
        try:
            # Setup report templates and default settings
            self._setup_report_templates()
            logger.info("Reporting services initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize reporting services: {e}")
    
    def _setup_report_templates(self):
        """Setup default report templates."""
        # This will be implemented when the database is ready
        logger.debug("Report templates setup completed")
