"""
Tests for Arena Doviz Customers app.
Tests customer management, validation, and API endpoints.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework import status
from .base import BaseTestCase, BaseAPITestCase, TestDataMixin, assert_audit_log_created
from apps.customers.models import Customer, CustomerDocument
from apps.locations.models import Location


class CustomerModelTest(BaseTestCase, TestDataMixin):
    """Test Customer model functionality."""
    
    def test_customer_creation(self):
        """Test creating a customer with all required fields."""
        customer = Customer.objects.create(
            customer_code='CUST002',
            customer_type=Customer.CustomerType.INDIVIDUAL,
            first_name='<PERSON>',
            last_name='<PERSON>',
            email='<EMAIL>',
            phone_number='+1987654321',
            preferred_location=self.location,
            status=Customer.Status.ACTIVE
        )

        self.assertEqual(customer.customer_code, 'CUST002')
        self.assertEqual(customer.customer_type, Customer.CustomerType.INDIVIDUAL)
        self.assertEqual(customer.first_name, 'Jane')
        self.assertEqual(customer.last_name, '<PERSON>')
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertEqual(customer.preferred_location, self.location)
        self.assertEqual(customer.status, Customer.Status.ACTIVE)
    
    def test_customer_display_name(self):
        """Test customer display name generation."""
        # Individual customer
        individual = self.create_test_customer(
            'IND001',
            customer_type=Customer.CustomerType.INDIVIDUAL,
            first_name='John',
            last_name='Doe'
        )
        self.assertEqual(individual.get_display_name(), 'John Doe')
        
        # Corporate customer
        corporate = self.create_test_customer(
            'CORP001',
            customer_type=Customer.CustomerType.CORPORATE,
            company_name='Test Corp Ltd'
        )
        self.assertEqual(corporate.get_display_name(), 'Test Corp Ltd')
        
        # Customer with only customer number
        minimal = self.create_test_customer(
            'MIN001',
            first_name='',
            last_name='',
            company_name=''
        )
        self.assertEqual(minimal.get_display_name(), 'MIN001')
    
    def test_customer_validation(self):
        """Test customer model validation."""
        # Test duplicate customer code
        with self.assertRaises(Exception):
            Customer.objects.create(
                customer_code=self.customer.customer_code,
                customer_type=Customer.CustomerType.INDIVIDUAL,
                first_name='Different',
                last_name='Customer',
                preferred_location=self.location
            )
        
        # Test invalid email
        with self.assertRaises(ValidationError):
            customer = Customer(
                customer_code='INVALID001',
                customer_type=Customer.CustomerType.INDIVIDUAL,
                first_name='Invalid',
                last_name='Email',
                email='invalid-email',
                preferred_location=self.location
            )
            customer.full_clean()

        # Test invalid phone number
        with self.assertRaises(ValidationError):
            customer = Customer(
                customer_code='INVALID002',
                customer_type=Customer.CustomerType.INDIVIDUAL,
                first_name='Invalid',
                last_name='Phone',
                phone_number='invalid-phone',
                preferred_location=self.location
            )
            customer.full_clean()
    
    def test_customer_code_generation(self):
        """Test automatic customer code generation."""
        customer1 = Customer.objects.create(
            customer_type=Customer.CustomerType.INDIVIDUAL,
            first_name='Auto',
            last_name='Number1',
            preferred_location=self.location
        )

        customer2 = Customer.objects.create(
            customer_type=Customer.CustomerType.INDIVIDUAL,
            first_name='Auto',
            last_name='Number2',
            preferred_location=self.location
        )

        self.assertIsNotNone(customer1.customer_code)
        self.assertIsNotNone(customer2.customer_code)
        self.assertNotEqual(customer1.customer_code, customer2.customer_code)
        self.assertTrue(customer1.customer_code.startswith('CUST'))
        self.assertTrue(customer2.customer_code.startswith('CORP'))
    
    def test_customer_balance_calculation(self):
        """Test customer balance calculation."""
        # Create transactions for the customer
        transaction1 = self.create_transaction(
            customer=self.customer,
            from_amount=1000,
            status='completed'
        )
        
        transaction2 = self.create_transaction(
            customer=self.customer,
            from_amount=500,
            status='completed'
        )
        
        # Test balance calculation
        balance = self.customer.get_balance(self.usd, self.location)
        # Balance should reflect the transactions
        from decimal import Decimal
        self.assertIsInstance(balance, Decimal)
    
    def test_customer_transaction_history(self):
        """Test customer transaction history."""
        # Create transactions
        transaction1 = self.create_transaction(customer=self.customer)
        transaction2 = self.create_transaction(customer=self.customer)
        
        # Get transaction history
        transactions = self.customer.get_transaction_history()
        
        self.assertIn(transaction1, transactions)
        self.assertIn(transaction2, transactions)
        self.assertEqual(transactions.count(), 2)


class CustomerDocumentModelTest(BaseTestCase, TestDataMixin):
    """Test CustomerDocument model functionality."""
    
    def test_document_creation(self):
        """Test creating a customer document."""
        from .base import create_test_file
        
        document = CustomerDocument.objects.create(
            customer=self.customer,
            document_type=CustomerDocument.DocumentType.PASSPORT,
            document_number='P123456789',
            file=create_test_file('passport.pdf'),
            uploaded_by=self.admin_user
        )
        
        self.assertEqual(document.customer, self.customer)
        self.assertEqual(document.document_type, CustomerDocument.DocumentType.PASSPORT)
        self.assertEqual(document.document_number, 'P123456789')
        self.assertEqual(document.uploaded_by, self.admin_user)
        self.assertTrue(document.is_active)
    
    def test_document_validation(self):
        """Test document validation."""
        # Test duplicate document number for same customer
        CustomerDocument.objects.create(
            customer=self.customer,
            document_type=CustomerDocument.DocumentType.PASSPORT,
            document_number='DUPLICATE123',
            uploaded_by=self.admin_user
        )
        
        with self.assertRaises(Exception):
            CustomerDocument.objects.create(
                customer=self.customer,
                document_type=CustomerDocument.DocumentType.PASSPORT,
                document_number='DUPLICATE123',
                uploaded_by=self.admin_user
            )


class CustomerAPITest(BaseAPITestCase, TestDataMixin):
    """Test Customer API endpoints."""
    
    def test_customer_list(self):
        """Test listing customers."""
        # Create additional test customers
        self.create_test_customer('TEST001')
        self.create_test_customer('TEST002')
        
        url = '/api/v1/customers/customers/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Should include the customers we created
        self.assertGreaterEqual(len(response.data['results']), 3)
    
    def test_customer_create(self):
        """Test creating a customer via API."""
        url = '/api/v1/customers/customers/'
        data = {
            'customer_code': 'API001',
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'first_name': 'API',
            'last_name': 'Customer',
            'email': '<EMAIL>',
            'phone_number': '+1555123456',
            'location': str(self.location.id)
        }

        response = self.client.post(url, data)
        self.assertAPISuccess(response, 201)

        # Verify customer was created
        customer = Customer.objects.get(customer_code='API001')
        self.assertEqual(customer.first_name, 'API')
        self.assertEqual(customer.last_name, 'Customer')
        self.assertEqual(customer.email, '<EMAIL>')
        
        # Verify audit log was created
        assert_audit_log_created(
            self, self.admin_user, 'create_customer', 'Customer', str(customer.id)
        )
    
    def test_customer_update(self):
        """Test updating a customer via API."""
        customer = self.create_test_customer('UPDATE001')
        url = f'/api/v1/customers/customers/{customer.id}/'
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'email': '<EMAIL>'
        }
        
        response = self.client.patch(url, data)
        self.assertAPISuccess(response)
        
        # Verify customer was updated
        customer.refresh_from_db()
        self.assertEqual(customer.first_name, 'Updated')
        self.assertEqual(customer.last_name, 'Name')
        self.assertEqual(customer.email, '<EMAIL>')
    
    def test_customer_delete(self):
        """Test soft deleting a customer via API."""
        customer = self.create_test_customer('DELETE001')
        url = f'/api/v1/customers/customers/{customer.id}/'
        
        response = self.client.delete(url)
        self.assertAPISuccess(response, 204)
        
        # Verify customer was soft deleted
        customer.refresh_from_db()
        self.assertTrue(customer.is_deleted)
    
    def test_customer_search(self):
        """Test customer search functionality."""
        # Create customers with searchable data
        customer1 = self.create_test_customer(
            'SEARCH001',
            first_name='Searchable',
            last_name='Customer'
        )
        customer2 = self.create_test_customer(
            'SEARCH002',
            first_name='Another',
            last_name='Person'
        )
        
        # Search by first name
        url = '/api/v1/customers/customers/?search=Searchable'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Should find the customer
        customer_ids = [c['id'] for c in response.data['results']]
        self.assertIn(str(customer1.id), customer_ids)
        self.assertNotIn(str(customer2.id), customer_ids)
    
    def test_customer_filter_by_location(self):
        """Test filtering customers by location."""
        # Create customer in different location
        other_location = Location.objects.create(
            name='Other Location',
            code='OTHER',
            address='Other Address',
            is_active=True
        )
        
        other_customer = self.create_test_customer(
            'OTHER001',
            location=other_location
        )
        
        # Filter by location
        url = f'/api/v1/customers/customers/?location={self.location.id}'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Should only include customers from specified location
        customer_ids = [c['id'] for c in response.data['results']]
        self.assertNotIn(str(other_customer.id), customer_ids)
    
    def test_customer_permissions(self):
        """Test customer permissions for different user roles."""
        customer = self.create_test_customer('PERM001')
        
        # Test viewer can read but not modify
        self.authenticate_as(self.viewer_user)
        
        # Can read
        url = f'/api/v1/customers/customers/{customer.id}/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Cannot update
        data = {'first_name': 'Should Not Work'}
        response = self.client.patch(url, data)
        self.assertAPIPermissionDenied(response)
        
        # Cannot delete
        response = self.client.delete(url)
        self.assertAPIPermissionDenied(response)
    
    def test_customer_balance_endpoint(self):
        """Test customer balance API endpoint."""
        # Create transactions for balance calculation
        self.create_transaction(
            customer=self.customer,
            from_amount=1000,
            status='completed'
        )
        
        url = f'/api/v1/customers/customers/{self.customer.id}/balance/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check balance structure - response.data is directly a list
        self.assertIsInstance(response.data, list)
    
    def test_customer_transaction_history_endpoint(self):
        """Test customer transaction history API endpoint."""
        # Create transactions
        transaction1 = self.create_transaction(customer=self.customer)
        transaction2 = self.create_transaction(customer=self.customer)
        
        url = f'/api/v1/customers/customers/{self.customer.id}/transactions/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check that transactions are returned
        transaction_ids = [t['id'] for t in response.data['results']]
        self.assertIn(str(transaction1.id), transaction_ids)
        self.assertIn(str(transaction2.id), transaction_ids)
    
    def test_customer_stats_endpoint(self):
        """Test customer statistics API endpoint."""
        url = '/api/v1/customers/customers/stats/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check stats structure
        self.assertIn('total_customers', response.data)
        self.assertIn('active_customers', response.data)
        self.assertIn('individual_customers', response.data)
        self.assertIn('corporate_customers', response.data)
        self.assertIn('customers_by_status', response.data)
        self.assertIn('customers_by_location', response.data)


class CustomerDocumentAPITest(BaseAPITestCase, TestDataMixin):
    """Test Customer Document API endpoints."""
    
    def test_document_upload(self):
        """Test uploading a customer document."""
        from .base import create_test_file
        
        url = '/api/v1/customers/documents/'
        data = {
            'customer': str(self.customer.id),
            'document_type': CustomerDocument.DocumentType.PASSPORT,
            'document_number': 'API123456',
            'file': create_test_file('test_passport.pdf')
        }
        
        response = self.client.post(url, data, format='multipart')
        self.assertAPISuccess(response, 201)
        
        # Verify document was created
        document = CustomerDocument.objects.get(id=response.data['id'])
        self.assertEqual(document.customer, self.customer)
        self.assertEqual(document.document_type, CustomerDocument.DocumentType.PASSPORT)
        self.assertEqual(document.document_number, 'API123456')
    
    def test_document_list_by_customer(self):
        """Test listing documents for a specific customer."""
        # Create documents
        doc1 = CustomerDocument.objects.create(
            customer=self.customer,
            document_type=CustomerDocument.DocumentType.PASSPORT,
            document_number='DOC001',
            uploaded_by=self.admin_user
        )
        
        doc2 = CustomerDocument.objects.create(
            customer=self.customer,
            document_type=CustomerDocument.DocumentType.ID_CARD,
            document_number='DOC002',
            uploaded_by=self.admin_user
        )
        
        url = f'/api/v1/customers/documents/?customer={self.customer.id}'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Should return both documents
        document_ids = [d['id'] for d in response.data['results']]
        self.assertIn(str(doc1.id), document_ids)
        self.assertIn(str(doc2.id), document_ids)
