"""
Tests for Arena Doviz Transactions app.
Tests transaction processing, balance calculations, and commission logic.
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from rest_framework import status
from decimal import Decimal
from .base import (
    BaseTestCase, BaseAPITestCase, BaseTransactionTestCase, TestDataMixin,
    assert_audit_log_created, assert_balance_entry_created
)
from apps.transactions.models import Transaction, TransactionType, BalanceEntry
from apps.transactions.commission_models import CommissionRule, CommissionTier
from apps.transactions.commission_utils import commission_calculator


class TransactionModelTest(BaseTestCase, TestDataMixin):
    """Test Transaction model functionality."""
    
    def test_transaction_creation(self):
        """Test creating a transaction with all required fields."""
        transaction = self.create_transaction()
        
        self.assertEqual(transaction.customer, self.customer)
        self.assertEqual(transaction.location, self.location)
        self.assertEqual(transaction.from_currency, self.usd)
        self.assertEqual(transaction.to_currency, self.aed)
        self.assertDecimalEqual(transaction.from_amount, Decimal('1000.00'))
        self.assertDecimalEqual(transaction.to_amount, Decimal('3670.00'))
        self.assertDecimalEqual(transaction.exchange_rate, Decimal('3.67'))
        self.assertEqual(transaction.status, Transaction.Status.DRAFT)
        self.assertIsNotNone(transaction.transaction_number)
    
    def test_transaction_number_generation(self):
        """Test automatic transaction number generation."""
        transaction1 = self.create_transaction()
        transaction2 = self.create_transaction()
        
        self.assertNotEqual(transaction1.transaction_number, transaction2.transaction_number)
        self.assertTrue(transaction1.transaction_number.startswith('TXN'))
        self.assertTrue(transaction2.transaction_number.startswith('TXN'))
    
    def test_transaction_validation(self):
        """Test transaction model validation."""
        # Test negative amounts
        with self.assertRaises(ValidationError):
            transaction = Transaction(
                customer=self.customer,
                location=self.location,
                from_currency=self.usd,
                to_currency=self.aed,
                from_amount=Decimal('-100.00'),
                to_amount=Decimal('367.00'),
                exchange_rate=Decimal('3.67')
            )
            transaction.full_clean()
        
        # Test same currency exchange
        with self.assertRaises(ValidationError):
            transaction = Transaction(
                customer=self.customer,
                location=self.location,
                from_currency=self.usd,
                to_currency=self.usd,
                from_amount=Decimal('100.00'),
                to_amount=Decimal('100.00'),
                exchange_rate=Decimal('1.00')
            )
            transaction.full_clean()
    
    def test_transaction_status_transitions(self):
        """Test valid transaction status transitions."""
        transaction = self.create_transaction()
        
        # Draft -> Pending
        transaction.status = Transaction.Status.PENDING
        transaction.save()
        self.assertEqual(transaction.status, Transaction.Status.PENDING)
        
        # Pending -> Approved
        transaction.status = Transaction.Status.APPROVED
        transaction.save()
        self.assertEqual(transaction.status, Transaction.Status.APPROVED)
        
        # Approved -> Completed
        transaction.status = Transaction.Status.COMPLETED
        transaction.save()
        self.assertEqual(transaction.status, Transaction.Status.COMPLETED)
    
    def test_commission_calculation(self):
        """Test automatic commission calculation."""
        # Create a commission rule
        rule = self.create_commission_rule(
            location=self.location,
            from_currency=self.usd,
            percentage_rate=Decimal('2.0')
        )
        
        transaction = self.create_transaction(commission_amount=Decimal('0'))
        
        # Commission should be calculated automatically
        expected_commission = transaction.from_amount * (rule.percentage_rate / 100)
        self.assertDecimalEqual(transaction.commission_amount, expected_commission)
    
    def test_balance_entry_creation(self):
        """Test that balance entries are created when transaction is completed."""
        transaction = self.create_transaction(status=Transaction.Status.COMPLETED)
        
        # Check that balance entries were created
        debit_entry = assert_balance_entry_created(
            self, transaction, BalanceEntry.EntryType.DEBIT
        )
        credit_entry = assert_balance_entry_created(
            self, transaction, BalanceEntry.EntryType.CREDIT
        )
        
        self.assertDecimalEqual(debit_entry.amount, transaction.from_amount)
        self.assertDecimalEqual(credit_entry.amount, transaction.to_amount)


class BalanceEntryModelTest(BaseTestCase, TestDataMixin):
    """Test BalanceEntry model functionality."""
    
    def test_balance_calculation(self):
        """Test running balance calculation."""
        # Create multiple transactions to test balance calculation
        transaction1 = self.create_transaction(
            from_amount=Decimal('1000.00'),
            status=Transaction.Status.COMPLETED
        )
        
        transaction2 = self.create_transaction(
            from_amount=Decimal('500.00'),
            status=Transaction.Status.COMPLETED
        )
        
        # Get current balance
        balance = BalanceEntry.get_current_balance(
            customer=self.customer,
            location=self.location,
            currency=self.usd
        )
        
        # Balance should reflect both transactions
        expected_balance = Decimal('-1500.00')  # Negative because we're selling USD
        self.assertDecimalEqual(balance, expected_balance)
    
    def test_balance_entry_validation(self):
        """Test balance entry validation."""
        transaction = self.create_transaction()
        
        # Test zero amount
        with self.assertRaises(ValidationError):
            entry = BalanceEntry(
                transaction=transaction,
                customer=self.customer,
                location=self.location,
                currency=self.usd,
                amount=Decimal('0.00'),
                entry_type=BalanceEntry.EntryType.DEBIT
            )
            entry.full_clean()


class CommissionRuleModelTest(BaseTestCase, TestDataMixin):
    """Test CommissionRule model functionality."""
    
    def test_commission_rule_creation(self):
        """Test creating a commission rule."""
        rule = self.create_commission_rule(
            name='Test Rule',
            commission_type=CommissionRule.CommissionType.PERCENTAGE,
            percentage_rate=Decimal('1.5'),
            location=self.location
        )
        
        self.assertEqual(rule.name, 'Test Rule')
        self.assertEqual(rule.commission_type, CommissionRule.CommissionType.PERCENTAGE)
        self.assertDecimalEqual(rule.percentage_rate, Decimal('1.5'))
        self.assertEqual(rule.location, self.location)
        self.assertTrue(rule.is_active)
    
    def test_commission_rule_validation(self):
        """Test commission rule validation."""
        # Test percentage rule without percentage rate
        with self.assertRaises(ValidationError):
            rule = CommissionRule(
                name='Invalid Rule',
                commission_type=CommissionRule.CommissionType.PERCENTAGE,
                fixed_amount=Decimal('10.00')  # Should have percentage_rate instead
            )
            rule.full_clean()
        
        # Test fixed amount rule without fixed amount
        with self.assertRaises(ValidationError):
            rule = CommissionRule(
                name='Invalid Rule',
                commission_type=CommissionRule.CommissionType.FIXED_AMOUNT,
                percentage_rate=Decimal('1.5')  # Should have fixed_amount instead
            )
            rule.full_clean()
    
    def test_commission_rule_applies_to_transaction(self):
        """Test commission rule application logic."""
        rule = self.create_commission_rule(
            location=self.location,
            from_currency=self.usd,
            to_currency=self.aed,
            min_amount=Decimal('100.00'),
            max_amount=Decimal('5000.00')
        )
        
        # Transaction that should match
        matching_transaction = self.create_transaction(
            location=self.location,
            from_currency=self.usd,
            to_currency=self.aed,
            from_amount=Decimal('1000.00')
        )
        
        self.assertTrue(rule.applies_to_transaction(matching_transaction))
        
        # Transaction that should not match (wrong currency)
        non_matching_transaction = self.create_transaction(
            location=self.location,
            from_currency=self.aed,  # Different currency
            to_currency=self.usd,
            from_amount=Decimal('1000.00')
        )
        
        self.assertFalse(rule.applies_to_transaction(non_matching_transaction))
        
        # Transaction that should not match (amount too small)
        small_transaction = self.create_transaction(
            location=self.location,
            from_currency=self.usd,
            to_currency=self.aed,
            from_amount=Decimal('50.00')  # Below minimum
        )
        
        self.assertFalse(rule.applies_to_transaction(small_transaction))
    
    def test_commission_calculation(self):
        """Test commission calculation for different rule types."""
        transaction = self.create_transaction(from_amount=Decimal('1000.00'))
        
        # Test percentage commission
        percentage_rule = self.create_commission_rule(
            commission_type=CommissionRule.CommissionType.PERCENTAGE,
            percentage_rate=Decimal('2.5')
        )
        
        amount, currency = percentage_rule.calculate_commission(transaction)
        expected_amount = Decimal('1000.00') * (Decimal('2.5') / 100)
        self.assertDecimalEqual(amount, expected_amount)
        
        # Test fixed amount commission
        fixed_rule = self.create_commission_rule(
            commission_type=CommissionRule.CommissionType.FIXED_AMOUNT,
            fixed_amount=Decimal('15.00')
        )
        
        amount, currency = fixed_rule.calculate_commission(transaction)
        self.assertDecimalEqual(amount, Decimal('15.00'))
        
        # Test hybrid commission
        hybrid_rule = self.create_commission_rule(
            commission_type=CommissionRule.CommissionType.HYBRID,
            percentage_rate=Decimal('1.0'),
            fixed_amount=Decimal('5.00')
        )
        
        amount, currency = hybrid_rule.calculate_commission(transaction)
        expected_amount = Decimal('5.00') + (Decimal('1000.00') * Decimal('0.01'))
        self.assertDecimalEqual(amount, expected_amount)


class CommissionCalculatorTest(BaseTestCase, TestDataMixin):
    """Test commission calculation utilities."""
    
    def test_commission_calculator(self):
        """Test the commission calculator utility."""
        # Create commission rules with different priorities
        high_priority_rule = self.create_commission_rule(
            name='High Priority',
            priority=50,
            percentage_rate=Decimal('3.0'),
            location=self.location,
            from_currency=self.usd
        )
        
        low_priority_rule = self.create_commission_rule(
            name='Low Priority',
            priority=100,
            percentage_rate=Decimal('1.0'),
            location=self.location,
            from_currency=self.usd
        )
        
        transaction = self.create_transaction(
            location=self.location,
            from_currency=self.usd,
            from_amount=Decimal('1000.00')
        )
        
        result = commission_calculator.calculate_commission(transaction)
        
        # Should use high priority rule
        self.assertEqual(result['rule'], high_priority_rule)
        expected_amount = Decimal('1000.00') * (Decimal('3.0') / 100)
        self.assertDecimalEqual(result['amount'], expected_amount)
    
    def test_commission_preview(self):
        """Test commission preview functionality."""
        rule = self.create_commission_rule(
            location=self.location,
            from_currency=self.usd,
            to_currency=self.aed,
            percentage_rate=Decimal('2.0')
        )
        
        transaction_data = {
            'location': self.location,
            'from_currency': self.usd,
            'to_currency': self.aed,
            'from_amount': Decimal('500.00')
        }
        
        result = commission_calculator.get_commission_preview(transaction_data)
        
        expected_amount = Decimal('500.00') * (Decimal('2.0') / 100)
        self.assertDecimalEqual(result['amount'], expected_amount)
        self.assertEqual(result['rule'], rule)


class TransactionAPITest(BaseAPITestCase, TestDataMixin):
    """Test Transaction API endpoints."""
    
    def test_transaction_list(self):
        """Test listing transactions."""
        # Create test transactions
        self.create_transaction()
        self.create_transaction()
        
        url = '/api/v1/transactions/transactions/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        self.assertGreaterEqual(len(response.data['results']), 2)
    
    def test_transaction_create(self):
        """Test creating a transaction via API."""
        url = '/api/v1/transactions/transactions/'
        data = {
            'customer': str(self.customer.id),
            'location': str(self.location.id),
            'transaction_type': str(self.transaction_type.id),
            'description': 'Test currency exchange transaction',
            'from_currency': str(self.usd.id),
            'to_currency': str(self.aed.id),
            'from_amount': '1000.00',
            'to_amount': '3670.00',
            'exchange_rate': '3.67',
            'delivery_method': 'cash'
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response, 201)
        
        # Verify transaction was created
        transaction = Transaction.objects.get(id=response.data['id'])
        self.assertEqual(transaction.customer, self.customer)
        self.assertDecimalEqual(transaction.from_amount, Decimal('1000.00'))
        
        # Verify audit log was created
        assert_audit_log_created(
            self, self.admin_user, 'create_transaction', model_name='Transaction', object_id=str(transaction.id)
        )
    
    def test_transaction_update(self):
        """Test updating a transaction via API."""
        transaction = self.create_transaction()
        url = f'/api/v1/transactions/transactions/{transaction.id}/'
        data = {
            'notes': 'Updated transaction notes'
        }
        
        response = self.client.patch(url, data)
        self.assertAPISuccess(response)
        
        transaction.refresh_from_db()
        self.assertEqual(transaction.notes, 'Updated transaction notes')
    
    def test_transaction_status_update(self):
        """Test updating transaction status."""
        transaction = self.create_transaction()
        url = f'/api/v1/transactions/transactions/{transaction.id}/'
        data = {
            'notes': 'Updated transaction notes'
        }

        response = self.client.patch(url, data)
        self.assertAPISuccess(response)
        
        transaction.refresh_from_db()
        self.assertEqual(transaction.notes, 'Updated transaction notes')
    
    def test_transaction_permissions(self):
        """Test transaction permissions for different user roles."""
        transaction = self.create_transaction()
        
        # Test viewer cannot update transactions
        self.authenticate_as(self.viewer_user)
        url = f'/api/v1/transactions/transactions/{transaction.id}/'
        data = {'notes': 'Should not be allowed'}
        
        response = self.client.patch(url, data)
        self.assertAPIPermissionDenied(response)
        
        # Test employee can update but not approve
        self.authenticate_as(self.employee_user)
        approve_url = f'/api/v1/transactions/transactions/{transaction.id}/approve/'
        
        response = self.client.post(approve_url)
        self.assertAPIPermissionDenied(response)
    
    def test_transaction_stats(self):
        """Test transaction statistics endpoint."""
        # Create test transactions
        self.create_transaction(status=Transaction.Status.COMPLETED)
        self.create_transaction(status=Transaction.Status.PENDING)
        
        url = '/api/v1/transactions/transactions/stats/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check stats structure
        self.assertIn('total_transactions', response.data)
        self.assertIn('total_volume_today', response.data)
        self.assertIn('transactions_by_status', response.data)


class CommissionRuleAPITest(BaseAPITestCase, TestDataMixin):
    """Test Commission Rule API endpoints."""
    
    def test_commission_rule_list(self):
        """Test listing commission rules."""
        self.create_commission_rule()
        
        url = '/api/v1/transactions/commission-rules/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        self.assertGreater(len(response.data['results']), 0)
    
    def test_commission_rule_create(self):
        """Test creating a commission rule via API."""
        url = '/api/v1/transactions/commission-rules/'
        data = {
            'name': 'API Test Rule',
            'commission_type': CommissionRule.CommissionType.PERCENTAGE,
            'percentage_rate': '2.5',
            'location': str(self.location.id),
            'is_active': True,
            'priority': 100
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response, 201)
        
        # Verify rule was created
        rule = CommissionRule.objects.get(id=response.data['id'])
        self.assertEqual(rule.name, 'API Test Rule')
        self.assertDecimalEqual(rule.percentage_rate, Decimal('2.5'))
    
    def test_commission_calculation_endpoint(self):
        """Test commission calculation API endpoint."""
        rule = self.create_commission_rule(
            location=self.location,
            from_currency=self.usd,
            to_currency=self.aed,
            percentage_rate=Decimal('1.5')
        )
        
        url = '/api/v1/transactions/commission-rules/calculate_commission/'
        data = {
            'location': str(self.location.id),
            'from_currency': str(self.usd.id),
            'to_currency': str(self.aed.id),
            'from_amount': '1000.00'
        }
        
        response = self.client.post(url, data)
        self.assertAPISuccess(response)
        
        commission = response.data['commission']
        expected_amount = Decimal('1000.00') * (Decimal('1.5') / 100)
        self.assertDecimalEqual(Decimal(commission['amount']), expected_amount)


class ExchangeRateManagementTest(BaseAPITestCase, TestDataMixin):
    """Test enhanced exchange rate management functionality."""

    def test_create_rate_with_approval(self):
        """Test creating a rate with approval workflow."""
        url = '/api/v1/currencies/rates/create_with_approval/'
        data = {
            'from_currency': str(self.usd.id),
            'to_currency': str(self.aed.id),
            'location': str(self.location.id),
            'buy_rate': '3.65',
            'sell_rate': '3.67',
            'notes': 'Test rate creation'
        }

        response = self.client.post(url, data)
        self.assertAPISuccess(response, 201)

        # Check response structure
        self.assertIn('rate', response.data)
        self.assertIn('approval_required', response.data)
        self.assertIn('message', response.data)

    def test_approve_rate(self):
        """Test rate approval functionality."""
        # Create a rate that requires approval
        from apps.currencies.models import ExchangeRate

        rate = ExchangeRate.objects.create(
            from_currency=self.usd,
            to_currency=self.aed,
            location=self.location,
            buy_rate=Decimal('3.65'),
            sell_rate=Decimal('3.67'),
            is_active=False,  # Pending approval
            created_by=self.employee_user
        )

        # Approve the rate
        url = f'/api/v1/currencies/rates/{rate.id}/approve/'
        data = {'notes': 'Approved for testing'}

        response = self.client.post(url, data)
        self.assertAPISuccess(response)

        # Verify rate was approved
        rate.refresh_from_db()
        self.assertTrue(rate.is_active)
        self.assertEqual(rate.approved_by, self.admin_user)
        self.assertIsNotNone(rate.approved_at)

    def test_reject_rate(self):
        """Test rate rejection functionality."""
        from apps.currencies.models import ExchangeRate

        rate = ExchangeRate.objects.create(
            from_currency=self.usd,
            to_currency=self.aed,
            location=self.location,
            buy_rate=Decimal('3.65'),
            sell_rate=Decimal('3.67'),
            is_active=False,
            created_by=self.employee_user
        )

        # Reject the rate
        url = f'/api/v1/currencies/rates/{rate.id}/reject/'
        data = {'reason': 'Rate too high'}

        response = self.client.post(url, data)
        self.assertAPISuccess(response)

        # Verify rate was rejected (soft deleted)
        rate.refresh_from_db()
        self.assertTrue(rate.is_deleted)
        self.assertIn('Rate too high', rate.notes)

    def test_validate_rate(self):
        """Test rate validation functionality."""
        url = '/api/v1/currencies/rates/validate_rate/'
        data = {
            'from_currency': str(self.usd.id),
            'to_currency': str(self.aed.id),
            'location': str(self.location.id),
            'buy_rate': '3.65',
            'sell_rate': '3.67'
        }

        response = self.client.post(url, data)
        self.assertAPISuccess(response)

        # Check validation result structure
        self.assertIn('valid', response.data)
        self.assertIn('warnings', response.data)
        self.assertIn('errors', response.data)
        self.assertIn('requires_approval', response.data)

    def test_get_rate_recommendations(self):
        """Test rate recommendations functionality."""
        # Create some historical rates
        from apps.currencies.models import ExchangeRate
        from django.utils import timezone
        from datetime import timedelta

        for i in range(5):
            ExchangeRate.objects.create(
                from_currency=self.usd,
                to_currency=self.aed,
                location=self.location,
                buy_rate=Decimal('3.65') + Decimal('0.01') * i,
                sell_rate=Decimal('3.67') + Decimal('0.01') * i,
                effective_from=timezone.now() - timedelta(days=i+1),
                is_active=False
            )

        url = '/api/v1/currencies/rates/recommendations/'
        params = {
            'from_currency': str(self.usd.id),
            'to_currency': str(self.aed.id),
            'location': str(self.location.id)
        }

        response = self.client.get(url, params)
        self.assertAPISuccess(response)

        # Check recommendations structure
        if 'recommendations' in response.data and response.data['recommendations']:
            self.assertIn('suggested_buy_rate', response.data['recommendations'])
            self.assertIn('suggested_sell_rate', response.data['recommendations'])
            self.assertIn('trend', response.data['recommendations'])

    def test_pending_approval_list(self):
        """Test listing rates pending approval."""
        from apps.currencies.models import ExchangeRate

        # Create pending rates
        pending_rate = ExchangeRate.objects.create(
            from_currency=self.usd,
            to_currency=self.aed,
            location=self.location,
            buy_rate=Decimal('3.65'),
            sell_rate=Decimal('3.67'),
            is_active=False,
            created_by=self.employee_user
        )

        url = '/api/v1/currencies/rates/pending_approval/'
        response = self.client.get(url)
        self.assertAPISuccess(response)

        # Should include the pending rate
        rate_ids = [rate['id'] for rate in response.data]
        self.assertIn(str(pending_rate.id), rate_ids)

    def test_rate_approval_permissions(self):
        """Test that only authorized users can approve rates."""
        from apps.currencies.models import ExchangeRate

        rate = ExchangeRate.objects.create(
            from_currency=self.usd,
            to_currency=self.aed,
            location=self.location,
            buy_rate=Decimal('3.65'),
            sell_rate=Decimal('3.67'),
            is_active=False,
            created_by=self.admin_user
        )

        # Test with employee user (should not have permission)
        self.authenticate_as(self.employee_user)

        url = f'/api/v1/currencies/rates/{rate.id}/approve/'
        response = self.client.post(url, {})
        self.assertAPIPermissionDenied(response)

        # Test with viewer user (should not have permission)
        self.authenticate_as(self.viewer_user)
        response = self.client.post(url, {})
        self.assertAPIPermissionDenied(response)
