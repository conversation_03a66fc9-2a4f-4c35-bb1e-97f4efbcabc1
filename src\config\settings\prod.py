"""
Production settings for Arena Doviz Exchange Accounting System.
"""

from .base import *
import os

# Security settings for production
DEBUG = False
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Database for production (using environment variables)
DATABASES['default'].update({
    'HOST': os.environ.get('DB_HOST', 'db'),
    'PORT': os.environ.get('DB_PORT', '5432'),
    'CONN_MAX_AGE': 60,
    'OPTIONS': {
        'sslmode': 'require',
    } if os.environ.get('DB_SSL', 'false').lower() == 'true' else {},
})

# Static files for production
STATIC_ROOT = '/var/www/arena/static'
MEDIA_ROOT = '/var/www/arena/media'

# Security settings
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = ********
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'

# Session security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Strict'
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Strict'

# Email configuration for production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Logging for production
LOGGING['handlers']['file']['filename'] = '/var/log/arena_doviz/arena_doviz.log'
LOGGING['handlers']['file']['level'] = 'WARNING'
LOGGING['root']['level'] = 'WARNING'

# Additional logging handler for errors
LOGGING['handlers']['error_file'] = {
    'level': 'ERROR',
    'class': 'logging.FileHandler',
    'filename': '/var/log/arena_doviz/errors.log',
    'formatter': 'verbose',
}

LOGGING['loggers']['django']['handlers'].append('error_file')
LOGGING['loggers']['apps']['handlers'].append('error_file')

# CORS settings for production
CORS_ALLOWED_ORIGINS = os.environ.get('CORS_ALLOWED_ORIGINS', '').split(',')
CORS_ALLOW_CREDENTIALS = True

# Production-specific Arena Doviz settings
ARENA_DOVIZ.update({
    'DEBUG_MODE': False,
    'MOCK_WHATSAPP': False,
    'ENABLE_TEST_DATA': False,
    'BACKUP_ENABLED': True,
    'AUDIT_LOG_RETENTION_DAYS': 365,
    'MAX_UPLOAD_SIZE': 10 * 1024 * 1024,  # 10MB
})

# Cache configuration for production
CACHES['default']['LOCATION'] = os.environ.get('REDIS_URL', 'redis://redis:6379/0')
CACHES['default']['OPTIONS']['CONNECTION_POOL_KWARGS'] = {
    'max_connections': 20,
    'retry_on_timeout': True,
}

# Create log directories
os.makedirs('/var/log/arena_doviz', exist_ok=True)
