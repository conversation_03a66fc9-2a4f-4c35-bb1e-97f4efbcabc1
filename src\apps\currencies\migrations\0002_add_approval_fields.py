# Generated manually for Arena Doviz Exchange Rate Approval Fields

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('currencies', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='exchangerate',
            name='approved_at',
            field=models.DateTimeField(blank=True, help_text='Date and time when this rate was approved', null=True, verbose_name='Approved at'),
        ),
        migrations.AddField(
            model_name='exchangerate',
            name='approved_by',
            field=models.ForeignKey(blank=True, help_text='User who approved this rate', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_rates', to=settings.AUTH_USER_MODEL, verbose_name='Approved by'),
        ),
    ]
