"""
Customer models for Arena Doviz Exchange Accounting System.
Handles both individual and corporate customers with comprehensive contact and balance tracking.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator, EmailValidator
from django.core.exceptions import ValidationError
from apps.core.models import BaseModel
import logging

logger = logging.getLogger(__name__)


class Customer(BaseModel):
    """
    Model representing customers of the exchange office.
    Supports both individual and corporate customers as per business requirements.
    """
    
    class CustomerType(models.TextChoices):
        INDIVIDUAL = 'individual', _('Individual')
        CORPORATE = 'corporate', _('Corporate')
    
    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        INACTIVE = 'inactive', _('Inactive')
        SUSPENDED = 'suspended', _('Suspended')
        BLOCKED = 'blocked', _('Blocked')
    
    # Basic information
    customer_type = models.CharField(
        _('Customer type'),
        max_length=20,
        choices=CustomerType.choices,
        default=CustomerType.INDIVIDUAL,
        help_text=_('Type of customer (individual or corporate)')
    )
    
    # Individual customer fields
    first_name = models.CharField(
        _('First name'),
        max_length=100,
        blank=True,
        help_text=_('First name (for individual customers)')
    )
    
    last_name = models.CharField(
        _('Last name'),
        max_length=100,
        blank=True,
        help_text=_('Last name (for individual customers)')
    )
    
    # Corporate customer fields
    company_name = models.CharField(
        _('Company name'),
        max_length=200,
        blank=True,
        help_text=_('Company name (for corporate customers)')
    )
    
    # Contact information
    phone_number = models.CharField(
        _('Phone number'),
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_('Phone number must be entered in the format: "+*********". Up to 15 digits allowed.')
            )
        ],
        help_text=_('Primary contact phone number')
    )
    
    email = models.EmailField(
        _('Email'),
        blank=True,
        validators=[EmailValidator()],
        help_text=_('Email address (optional)')
    )
    
    # Address information
    address = models.TextField(
        _('Address'),
        blank=True,
        help_text=_('Full address')
    )
    
    city = models.CharField(
        _('City'),
        max_length=100,
        blank=True,
        help_text=_('City')
    )
    
    country = models.CharField(
        _('Country'),
        max_length=100,
        blank=True,
        help_text=_('Country')
    )
    
    # Business information
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Additional description or business details')
    )
    
    # WhatsApp integration
    whatsapp_group_id = models.CharField(
        _('WhatsApp group ID'),
        max_length=100,
        blank=True,
        help_text=_('WhatsApp group ID for customer communications (7-8 person team + customer)')
    )
    
    # Customer status and settings
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE,
        help_text=_('Current status of the customer')
    )
    
    credit_limit = models.DecimalField(
        _('Credit limit'),
        max_digits=15,
        decimal_places=2,
        default=0.00,
        help_text=_('Maximum negative balance allowed for this customer')
    )
    
    # Preferences
    preferred_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='preferred_by_customers',
        verbose_name=_('Preferred currency'),
        help_text=_('Customer\'s preferred currency for transactions')
    )
    
    preferred_location = models.ForeignKey(
        'locations.Location',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='preferred_by_customers',
        verbose_name=_('Preferred location'),
        help_text=_('Customer\'s preferred location for transactions')
    )
    
    # Internal notes
    notes = models.TextField(
        _('Internal notes'),
        blank=True,
        help_text=_('Internal notes about this customer (not visible to customer)')
    )
    
    # Customer identification
    customer_code = models.CharField(
        _('Customer code'),
        max_length=20,
        unique=True,
        blank=True,
        help_text=_('Unique customer code (auto-generated if not provided)')
    )
    
    # Risk assessment
    risk_level = models.CharField(
        _('Risk level'),
        max_length=20,
        choices=[
            ('low', _('Low')),
            ('medium', _('Medium')),
            ('high', _('High')),
        ],
        default='low',
        help_text=_('Risk assessment level for this customer')
    )
    
    # Metadata
    registration_date = models.DateField(
        _('Registration date'),
        auto_now_add=True,
        help_text=_('Date when customer was registered')
    )
    
    last_transaction_date = models.DateTimeField(
        _('Last transaction date'),
        null=True,
        blank=True,
        help_text=_('Date of the last transaction with this customer')
    )
    
    class Meta:
        verbose_name = _('Customer')
        verbose_name_plural = _('Customers')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer_type']),
            models.Index(fields=['status']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['email']),
            models.Index(fields=['customer_code']),
            models.Index(fields=['last_transaction_date']),
            models.Index(fields=['preferred_location']),
        ]
    
    def __str__(self):
        return self.get_display_name()
    
    def get_display_name(self):
        """Return appropriate display name based on customer type."""
        if self.customer_type == self.CustomerType.CORPORATE:
            return self.company_name or f"Corporate Customer {self.customer_code}"
        else:
            if self.first_name and self.last_name:
                return f"{self.first_name} {self.last_name}"
            elif self.first_name:
                return self.first_name
            else:
                return f"Customer {self.customer_code}"
    
    def get_full_name(self):
        """Return full name for individual customers."""
        if self.customer_type == self.CustomerType.INDIVIDUAL:
            return f"{self.first_name} {self.last_name}".strip()
        return self.company_name
    
    def get_contact_info(self):
        """Return formatted contact information."""
        contact_parts = []
        if self.phone_number:
            contact_parts.append(f"Phone: {self.phone_number}")
        if self.email:
            contact_parts.append(f"Email: {self.email}")
        return " | ".join(contact_parts)
    
    def get_full_address(self):
        """Return formatted full address."""
        address_parts = [self.address, self.city, self.country]
        return ", ".join(filter(None, address_parts))
    
    def is_active(self):
        """Check if customer is active."""
        return self.status == self.Status.ACTIVE
    
    def is_corporate(self):
        """Check if customer is corporate."""
        return self.customer_type == self.CustomerType.CORPORATE
    
    def is_individual(self):
        """Check if customer is individual."""
        return self.customer_type == self.CustomerType.INDIVIDUAL
    
    def get_balance_summary(self):
        """Get summary of customer balances across all currencies."""
        from apps.transactions.models import BalanceEntry
        
        try:
            balances = BalanceEntry.objects.filter(
                customer=self,
                is_deleted=False
            ).values('currency__code', 'currency__symbol').annotate(
                total_balance=models.Sum('amount')
            ).order_by('currency__code')
            
            return {
                balance['currency__code']: {
                    'amount': balance['total_balance'],
                    'symbol': balance['currency__symbol']
                }
                for balance in balances if balance['total_balance'] != 0
            }
            
        except Exception as e:
            logger.error(f"Error getting balance summary for customer {self.customer_code}: {e}")
            return {}
    
    def get_total_balance_in_currency(self, currency_code):
        """Get total balance for a specific currency."""
        from apps.transactions.models import BalanceEntry
        
        try:
            balance = BalanceEntry.objects.filter(
                customer=self,
                currency__code=currency_code,
                is_deleted=False
            ).aggregate(total=models.Sum('amount'))['total']
            
            return balance or 0
            
        except Exception as e:
            logger.error(f"Error getting {currency_code} balance for customer {self.customer_code}: {e}")
            return 0
    
    def has_negative_balance(self):
        """Check if customer has any negative balances."""
        balances = self.get_balance_summary()
        return any(balance['amount'] < 0 for balance in balances.values())
    
    def is_within_credit_limit(self, currency_code, amount):
        """Check if a transaction would keep customer within credit limit."""
        current_balance = self.get_total_balance_in_currency(currency_code)
        new_balance = current_balance + amount
        
        if new_balance >= 0:
            return True
        
        return abs(new_balance) <= self.credit_limit
    
    def get_transaction_count(self):
        """Get total number of transactions for this customer."""
        from apps.transactions.models import Transaction
        
        return Transaction.objects.filter(
            customer=self,
            is_deleted=False
        ).count()
    
    def get_last_transaction(self):
        """Get the most recent transaction for this customer."""
        from apps.transactions.models import Transaction

        return Transaction.objects.filter(
            customer=self,
            is_deleted=False
        ).order_by('-created_at').first()

    def get_balance(self, currency, location=None):
        """Get customer balance for a specific currency and location."""
        from apps.transactions.models import BalanceEntry
        from decimal import Decimal

        try:
            queryset = BalanceEntry.objects.filter(
                customer=self,
                currency=currency,
                is_deleted=False
            )

            if location:
                queryset = queryset.filter(location=location)

            balance = queryset.aggregate(total=models.Sum('amount'))['total']
            return balance or Decimal('0')

        except Exception as e:
            logger.error(f"Error getting balance for customer {self.customer_code}: {e}")
            return Decimal('0')

    def get_transaction_history(self, limit=None):
        """Get transaction history for this customer."""
        from apps.transactions.models import Transaction

        queryset = Transaction.objects.filter(
            customer=self,
            is_deleted=False
        ).order_by('-created_at')

        if limit:
            queryset = queryset[:limit]

        return queryset
    
    def clean(self):
        """Validate customer data."""
        super().clean()
        
        # Validate required fields based on customer type
        if self.customer_type == self.CustomerType.INDIVIDUAL:
            if not self.first_name and not self.last_name:
                raise ValidationError({
                    'first_name': _('First name or last name is required for individual customers')
                })
        elif self.customer_type == self.CustomerType.CORPORATE:
            if not self.company_name:
                raise ValidationError({
                    'company_name': _('Company name is required for corporate customers')
                })
        
        # Validate credit limit
        if self.credit_limit < 0:
            raise ValidationError({
                'credit_limit': _('Credit limit cannot be negative')
            })
    
    def save(self, *args, **kwargs):
        """
        Override save to handle business logic.
        """
        # Auto-generate customer code if not provided
        if not self.customer_code:
            self.customer_code = self._generate_customer_code()
        
        super().save(*args, **kwargs)
        
        logger.info(f"Customer saved: {self.customer_code} - {self.get_display_name()}")
    
    def _generate_customer_code(self):
        """Generate a unique customer code."""
        import random
        import string
        
        # Generate code based on customer type
        if self.customer_type == self.CustomerType.CORPORATE:
            prefix = 'CORP'
        else:
            prefix = 'CUST'
        
        # Find the next available number
        last_customer = Customer.objects.filter(
            customer_code__startswith=prefix
        ).order_by('-customer_code').first()
        
        if last_customer and last_customer.customer_code:
            try:
                last_number = int(last_customer.customer_code[len(prefix):])
                next_number = last_number + 1
            except (ValueError, IndexError):
                next_number = 1
        else:
            next_number = 1
        
        return f"{prefix}{next_number:06d}"
    
    @classmethod
    def get_active_customers(cls):
        """Get all active customers."""
        return cls.objects.filter(
            status=cls.Status.ACTIVE,
            is_deleted=False
        ).order_by('customer_code')
    
    @classmethod
    def search_customers(cls, query):
        """Search customers by name, code, phone, or email."""
        if not query:
            return cls.objects.none()
        
        return cls.objects.filter(
            models.Q(first_name__icontains=query) |
            models.Q(last_name__icontains=query) |
            models.Q(company_name__icontains=query) |
            models.Q(customer_code__icontains=query) |
            models.Q(phone_number__icontains=query) |
            models.Q(email__icontains=query),
            is_deleted=False
        ).order_by('customer_code')


class CustomerContact(BaseModel):
    """
    Model for storing additional contact information for customers.
    Allows multiple contacts per customer for corporate accounts.
    """
    
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='contacts',
        verbose_name=_('Customer')
    )
    
    contact_type = models.CharField(
        _('Contact type'),
        max_length=50,
        choices=[
            ('primary', _('Primary Contact')),
            ('secondary', _('Secondary Contact')),
            ('billing', _('Billing Contact')),
            ('technical', _('Technical Contact')),
            ('emergency', _('Emergency Contact')),
        ],
        default='primary',
        help_text=_('Type of contact')
    )
    
    name = models.CharField(
        _('Contact name'),
        max_length=200,
        help_text=_('Name of the contact person')
    )
    
    title = models.CharField(
        _('Title/Position'),
        max_length=100,
        blank=True,
        help_text=_('Job title or position')
    )
    
    phone_number = models.CharField(
        _('Phone number'),
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_('Phone number must be entered in the format: "+*********". Up to 15 digits allowed.')
            )
        ],
        help_text=_('Contact phone number')
    )
    
    email = models.EmailField(
        _('Email'),
        blank=True,
        help_text=_('Contact email address')
    )
    
    is_primary = models.BooleanField(
        _('Is primary contact'),
        default=False,
        help_text=_('Whether this is the primary contact for the customer')
    )
    
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this contact')
    )
    
    class Meta:
        verbose_name = _('Customer Contact')
        verbose_name_plural = _('Customer Contacts')
        ordering = ['-is_primary', 'name']
        indexes = [
            models.Index(fields=['customer', 'contact_type']),
            models.Index(fields=['is_primary']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['email']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.customer.get_display_name()})"
    
    def save(self, *args, **kwargs):
        """
        Override save to handle primary contact logic.
        """
        # Ensure only one primary contact per customer
        if self.is_primary:
            CustomerContact.objects.filter(
                customer=self.customer,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        
        super().save(*args, **kwargs)
        
        logger.debug(f"Customer contact saved: {self.name} for {self.customer.customer_code}")


class CustomerDocument(BaseModel):
    """
    Model for storing customer documents and attachments.
    """

    class DocumentType(models.TextChoices):
        ID_CARD = 'id_card', _('ID Card')
        PASSPORT = 'passport', _('Passport')
        BUSINESS_LICENSE = 'business_license', _('Business License')
        TAX_CERTIFICATE = 'tax_certificate', _('Tax Certificate')
        BANK_STATEMENT = 'bank_statement', _('Bank Statement')
        CONTRACT = 'contract', _('Contract')
        OTHER = 'other', _('Other')

    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='documents',
        verbose_name=_('Customer')
    )

    document_type = models.CharField(
        _('Document type'),
        max_length=50,
        choices=DocumentType.choices,
        help_text=_('Type of document')
    )
    
    title = models.CharField(
        _('Document title'),
        max_length=200,
        help_text=_('Title or description of the document')
    )
    
    file = models.FileField(
        _('File'),
        upload_to='customer_documents/%Y/%m/',
        help_text=_('Document file')
    )
    
    file_size = models.PositiveIntegerField(
        _('File size'),
        null=True,
        blank=True,
        help_text=_('File size in bytes')
    )
    
    mime_type = models.CharField(
        _('MIME type'),
        max_length=100,
        blank=True,
        help_text=_('MIME type of the file')
    )
    
    expiry_date = models.DateField(
        _('Expiry date'),
        null=True,
        blank=True,
        help_text=_('Document expiry date (if applicable)')
    )
    
    is_verified = models.BooleanField(
        _('Is verified'),
        default=False,
        help_text=_('Whether this document has been verified')
    )
    
    verified_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_documents',
        verbose_name=_('Verified by'),
        help_text=_('User who verified this document')
    )
    
    verified_at = models.DateTimeField(
        _('Verified at'),
        null=True,
        blank=True,
        help_text=_('Date and time when document was verified')
    )
    
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this document')
    )
    
    class Meta:
        verbose_name = _('Customer Document')
        verbose_name_plural = _('Customer Documents')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'document_type']),
            models.Index(fields=['is_verified']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} ({self.customer.get_display_name()})"
    
    def is_expired(self):
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        
        from django.utils import timezone
        return timezone.now().date() > self.expiry_date
    
    def get_file_extension(self):
        """Get file extension."""
        if self.file:
            return self.file.name.split('.')[-1].lower()
        return ''
    
    def save(self, *args, **kwargs):
        """
        Override save to handle file metadata.
        """
        if self.file:
            # Set file size
            if hasattr(self.file, 'size'):
                self.file_size = self.file.size
            
            # Set MIME type based on file extension
            if not self.mime_type:
                extension = self.get_file_extension()
                mime_types = {
                    'pdf': 'application/pdf',
                    'jpg': 'image/jpeg',
                    'jpeg': 'image/jpeg',
                    'png': 'image/png',
                    'doc': 'application/msword',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'xls': 'application/vnd.ms-excel',
                    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                }
                self.mime_type = mime_types.get(extension, 'application/octet-stream')
        
        super().save(*args, **kwargs)
        
        logger.info(f"Customer document saved: {self.title} for {self.customer.customer_code}")
