"""
Test settings for Arena Doviz Exchange Accounting System.
Optimized for fast test execution and isolation.
"""

from .base import *
import tempfile
import os
import sys

# Test database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Password hashers - use faster hashing for tests
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Cache configuration for tests
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# Email backend for tests
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Media files for tests
MEDIA_ROOT = tempfile.mkdtemp()

# Static files for tests
STATIC_ROOT = tempfile.mkdtemp()

# Disable logging during tests
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
    },
    'root': {
        'handlers': ['null'],
    },
    'loggers': {
        'django': {
            'handlers': ['null'],
            'propagate': False,
        },
        'apps': {
            'handlers': ['null'],
            'propagate': False,
        },
    }
}

# Disable CSRF for API tests
REST_FRAMEWORK['DEFAULT_AUTHENTICATION_CLASSES'] = [
    'rest_framework_simplejwt.authentication.JWTAuthentication',
    'rest_framework.authentication.SessionAuthentication',
]

# JWT settings for tests
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=5),
    'REFRESH_TOKEN_LIFETIME': timedelta(minutes=10),
    'ROTATE_REFRESH_TOKENS': False,  # Disable rotation for simpler tests
})

# Test-specific Arena Doviz settings
ARENA_DOVIZ.update({
    'DEBUG_MODE': True,
    'MOCK_WHATSAPP': True,
    'ENABLE_TEST_DATA': True,
    'SKIP_EXTERNAL_APIS': True,
    'FAST_TEST_MODE': True,
})

# Celery settings for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Security settings for tests
SECRET_KEY = 'test-secret-key-not-for-production'
DEBUG = True
ALLOWED_HOSTS = ['*']

# Disable HTTPS redirects in tests
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0

# CORS settings for tests
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# File upload settings for tests
FILE_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024  # 1MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 1024 * 1024  # 1MB

# Test coverage settings
if 'test' in sys.argv or 'pytest' in sys.modules:
    # Additional test-specific settings
    DATABASES['default']['OPTIONS']['timeout'] = 30
    
    # Disable unnecessary middleware for tests
    MIDDLEWARE = [
        'django.middleware.security.SecurityMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'corsheaders.middleware.CorsMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
    ]

# Test data fixtures
FIXTURE_DIRS = [
    os.path.join(BASE_DIR, 'tests', 'fixtures'),
]

# Test runner configuration
TEST_RUNNER = 'django.test.runner.DiscoverRunner'

# Test discovery settings
TEST_DISCOVERY_ROOT = os.path.join(BASE_DIR, 'tests')

# Parallel test execution
TEST_PARALLEL = True

# Keep test database for debugging (set to False for CI)
KEEP_TEST_DATABASE = os.environ.get('KEEP_TEST_DB', 'False').lower() == 'true'

# Test output settings
TEST_OUTPUT_VERBOSE = os.environ.get('TEST_VERBOSE', 'False').lower() == 'true'

if TEST_OUTPUT_VERBOSE:
    LOGGING['handlers']['console'] = {
        'class': 'logging.StreamHandler',
        'formatter': 'verbose',
    }
    LOGGING['loggers']['tests'] = {
        'handlers': ['console'],
        'level': 'DEBUG',
        'propagate': False,
    }

# Mock external services for tests
MOCK_EXTERNAL_SERVICES = True

# Test data cleanup
CLEANUP_TEST_DATA = True

# Performance testing settings
PERFORMANCE_TEST_ENABLED = os.environ.get('PERFORMANCE_TEST', 'False').lower() == 'true'

if PERFORMANCE_TEST_ENABLED:
    # Enable query counting for performance tests
    INSTALLED_APPS += ['django_extensions']
    
    # Database query logging
    LOGGING['loggers']['django.db.backends'] = {
        'handlers': ['console'],
        'level': 'DEBUG',
        'propagate': False,
    }

# Integration test settings
INTEGRATION_TEST_ENABLED = os.environ.get('INTEGRATION_TEST', 'False').lower() == 'true'

if INTEGRATION_TEST_ENABLED:
    # Use real database for integration tests
    DATABASES['default'] = {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('TEST_DB_NAME', 'test_exchange_db'),
        'USER': os.environ.get('TEST_DB_USER', 'postgres'),
        'PASSWORD': os.environ.get('TEST_DB_PASSWORD', 'postgres'),
        'HOST': os.environ.get('TEST_DB_HOST', 'localhost'),
        'PORT': os.environ.get('TEST_DB_PORT', '5432'),
        'TEST': {
            'NAME': 'test_exchange_db_integration',
        }
    }
    
    # Re-enable migrations for integration tests
    MIGRATION_MODULES = {}

# Test coverage configuration
COVERAGE_ENABLED = os.environ.get('COVERAGE', 'False').lower() == 'true'

if COVERAGE_ENABLED:
    INSTALLED_APPS += ['coverage']
    
    # Coverage settings
    COVERAGE_MODULE_EXCLUDES = [
        'tests$', 'settings$', 'urls$', 'locale$',
        'migrations', 'fixtures', 'venv', 'virtualenv'
    ]
    
    COVERAGE_REPORT_HTML_OUTPUT_DIR = os.path.join(BASE_DIR, 'htmlcov')

# Selenium test settings
SELENIUM_TEST_ENABLED = os.environ.get('SELENIUM_TEST', 'False').lower() == 'true'

if SELENIUM_TEST_ENABLED:
    # Selenium WebDriver settings
    SELENIUM_WEBDRIVER = os.environ.get('SELENIUM_WEBDRIVER', 'chrome')
    SELENIUM_HEADLESS = os.environ.get('SELENIUM_HEADLESS', 'True').lower() == 'true'
    SELENIUM_TIMEOUT = int(os.environ.get('SELENIUM_TIMEOUT', '10'))
    
    # Live server settings for Selenium tests
    LIVE_SERVER_PORT = '8081-8090'
    
    # Static files serving for Selenium tests
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# API test settings
API_TEST_THROTTLING_DISABLED = True

if API_TEST_THROTTLING_DISABLED:
    # Disable API throttling for tests
    REST_FRAMEWORK['DEFAULT_THROTTLE_CLASSES'] = []
    REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {}

# Test data factories
USE_TEST_FACTORIES = True

if USE_TEST_FACTORIES:
    try:
        import factory
        INSTALLED_APPS += ['factory']
    except ImportError:
        pass

# Memory usage optimization for tests
if 'test' in sys.argv:
    # Reduce memory usage during tests
    DATABASES['default']['CONN_MAX_AGE'] = 0
    
    # Disable unnecessary features
    USE_TZ = True  # Keep timezone support
    USE_I18N = False  # Disable internationalization for faster tests
    USE_L10N = False  # Disable localization for faster tests

# Test result reporting
TEST_RESULTS_DIR = os.path.join(BASE_DIR, 'test_results')
os.makedirs(TEST_RESULTS_DIR, exist_ok=True)

# JUnit XML output for CI
JUNIT_XML_OUTPUT = os.path.join(TEST_RESULTS_DIR, 'junit.xml')

# Test timing
TEST_TIMING_ENABLED = os.environ.get('TEST_TIMING', 'False').lower() == 'true'
