"""
Locations app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class LocationsConfig(AppConfig):
    """Configuration for the locations app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.locations'
    verbose_name = 'Arena Doviz Locations'
    
    def ready(self):
        """
        Initialize the locations app when Django starts.
        """
        logger.info("Arena Doviz Locations app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Locations app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import locations app signals: {e}")
        
        # Initialize default locations
        self._initialize_default_locations()
    
    def _initialize_default_locations(self):
        """Initialize default locations based on business requirements."""
        logger.debug("Initializing default locations...")
        
        try:
            # This will be called after migrations, so we need to check if we can access the model
            from django.db import connection
            from django.db.utils import OperationalError
            
            # Check if the locations table exists
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'locations_location'
                    );
                """)
                table_exists = cursor.fetchone()[0]
            
            if table_exists:
                self._create_default_locations()
            else:
                logger.debug("Locations table does not exist yet, skipping default location creation")
                
        except (OperationalError, Exception) as e:
            logger.debug(f"Could not initialize default locations: {e}")
    
    def _create_default_locations(self):
        """Create default locations if they don't exist."""
        from .models import Location
        
        # Default locations based on business requirements
        default_locations = [
            {
                'name': 'Istanbul',
                'code': 'IST',
                'country': 'Turkey',
                'city': 'Istanbul',
                'timezone': 'Europe/Istanbul',
                'is_active': True,
                'is_main_office': False,
            },
            {
                'name': 'Tabriz',
                'code': 'TBZ',
                'country': 'Iran',
                'city': 'Tabriz',
                'timezone': 'Asia/Tehran',
                'is_active': True,
                'is_main_office': True,  # Main office
            },
            {
                'name': 'Tehran',
                'code': 'THR',
                'country': 'Iran',
                'city': 'Tehran',
                'timezone': 'Asia/Tehran',
                'is_active': True,
                'is_main_office': False,
            },
            {
                'name': 'Dubai',
                'code': 'DXB',
                'country': 'UAE',
                'city': 'Dubai',
                'timezone': 'Asia/Dubai',
                'is_active': True,
                'is_main_office': False,
            },
            {
                'name': 'China',
                'code': 'CHN',
                'country': 'China',
                'city': 'Shanghai',
                'timezone': 'Asia/Shanghai',
                'is_active': True,
                'is_main_office': False,
            },
        ]
        
        for location_data in default_locations:
            location, created = Location.objects.get_or_create(
                code=location_data['code'],
                defaults=location_data
            )
            if created:
                logger.info(f"Created default location: {location.name}")
            else:
                logger.debug(f"Default location already exists: {location.name}")
