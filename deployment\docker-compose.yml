version: '3.8'

services:
  db:
    image: postgres:15-alpine
    container_name: arena_db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-exchange_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: arena_redis
    ports:
      - "6379:6379"

  web:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    container_name: arena_web
    command: ["bash", "-lc", "python manage.py migrate && gunicorn config.wsgi:application -b 0.0.0.0:8000 --workers 3 --timeout 120"]
    env_file:
      - ../.env
    environment:
      DATABASE_URL: ${DATABASE_URL:-************************************/exchange_db}
      REDIS_URL: ${REDIS_URL:-redis://redis:6379/0}
      DJANGO_SETTINGS_MODULE: config.settings.prod
    working_dir: /app/src
    volumes:
      - ..:/app
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started

  nginx:
    image: nginx:1.25-alpine
    container_name: arena_nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ../src/static:/var/www/arena/static
      - ../src/media:/var/www/arena/media
    depends_on:
      - web

volumes:
  db_data:

