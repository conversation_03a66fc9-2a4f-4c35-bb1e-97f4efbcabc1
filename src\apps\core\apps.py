"""
Core app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class CoreConfig(AppConfig):
    """Configuration for the core app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.core'
    verbose_name = 'Arena Doviz Core'
    
    def ready(self):
        """
        Initialize the core app when Django starts.
        This method is called when the app is ready.
        """
        logger.info("Arena Doviz Core app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Core app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import core app signals: {e}")
        
        # Initialize core services
        self._initialize_core_services()
    
    def _initialize_core_services(self):
        """Initialize core services and utilities."""
        logger.debug("Initializing core services...")
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize cache
        self._setup_cache()
        
        logger.info("Core services initialized successfully")
    
    def _setup_logging(self):
        """Setup application-specific logging."""
        try:
            # Configure application logging
            app_logger = logging.getLogger('apps')
            app_logger.info("Application logging configured")
        except Exception as e:
            logger.error(f"Failed to setup logging: {e}")
    
    def _setup_cache(self):
        """Setup cache configuration."""
        try:
            from django.core.cache import cache
            # Test cache connection
            cache.set('core_app_ready', True, 60)
            if cache.get('core_app_ready'):
                logger.debug("Cache connection verified")
            else:
                logger.warning("Cache connection test failed")
        except Exception as e:
            logger.error(f"Failed to setup cache: {e}")
