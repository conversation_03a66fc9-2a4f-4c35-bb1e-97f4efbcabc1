"""
Web URL configuration for Arena Doviz Transactions app.
Handles HTML template routes for transaction management interface.
"""

from django.urls import path
from . import web_views

app_name = 'transactions_web'

urlpatterns = [
    # Transaction management pages
    path('', web_views.transaction_list, name='list'),
    path('add/', web_views.transaction_add, name='add'),
    path('<uuid:transaction_id>/', web_views.transaction_detail, name='detail'),
    path('<uuid:transaction_id>/edit/', web_views.transaction_edit, name='edit'),
    path('<uuid:transaction_id>/approve/', web_views.transaction_approve_page, name='approve'),

    # Special transaction pages
    path('pending-approvals/', web_views.pending_approvals, name='pending_approvals'),
    path('reports/', web_views.transaction_reports, name='reports'),
    path('dashboard/', web_views.TransactionDashboardView.as_view(), name='dashboard'),

    # AJAX endpoints for web interface
    path('api/stats/', web_views.get_transaction_stats, name='api_stats'),
    path('api/customer/<uuid:customer_id>/balance/', web_views.get_customer_balance, name='api_customer_balance'),
    path('api/exchange-rates/', web_views.get_exchange_rates, name='api_exchange_rates'),
    path('api/quick-transaction/', web_views.quick_transaction, name='api_quick_transaction'),
]
