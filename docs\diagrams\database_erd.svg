<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="tableGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>
  
  <!-- Title -->
  <rect x="0" y="0" width="1400" height="50" fill="#2c3e50"/>
  <text x="700" y="30" text-anchor="middle" fill="white" font-size="20" font-weight="bold" font-family="Arial">
    Exchange Accounting System - Database ERD
  </text>
  
  <!-- Users Table -->
  <g transform="translate(50, 80)">
    <rect width="200" height="180" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Users</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">username</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">email</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">first_name</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">last_name</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">phone</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">is_active</text>
    <text x="10" y="150" fill="white" font-size="10" font-family="Arial">is_staff</text>
    <text x="10" y="165" fill="white" font-size="10" font-family="Arial">created_at</text>
  </g>
  
  <!-- User Roles Table -->
  <g transform="translate(300, 80)">
    <rect width="180" height="120" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="180" height="30" fill="#2c3e50" rx="5"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">User Roles</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">name</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">description</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">permissions</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">created_at</text>
  </g>
  
  <!-- Locations Table -->
  <g transform="translate(520, 80)">
    <rect width="180" height="150" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="180" height="30" fill="#2c3e50" rx="5"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Locations</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">name</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">code</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">address</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">phone</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">🔗 manager_id (FK)</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">is_active</text>
  </g>
  
  <!-- Currencies Table -->
  <g transform="translate(750, 80)">
    <rect width="180" height="135" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="180" height="30" fill="#2c3e50" rx="5"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Currencies</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">code</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">name</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">symbol</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">decimal_places</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">is_active</text>
  </g>
  
  <!-- Customers Table -->
  <g transform="translate(50, 280)">
    <rect width="200" height="195" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Customers</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">customer_code</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">first_name</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">last_name</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">phone</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">email</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">company_name</text>
    <text x="10" y="150" fill="white" font-size="10" font-family="Arial">description</text>
    <text x="10" y="165" fill="white" font-size="10" font-family="Arial">whatsapp_group_id</text>
    <text x="10" y="180" fill="white" font-size="10" font-family="Arial">🔗 created_by (FK)</text>
  </g>
  
  <!-- Exchange Rates Table -->
  <g transform="translate(980, 80)">
    <rect width="200" height="165" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Exchange Rates</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">🔗 from_currency_id (FK)</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">🔗 to_currency_id (FK)</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">🔗 location_id (FK)</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">buy_rate</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">sell_rate</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">rate_date</text>
    <text x="10" y="150" fill="white" font-size="10" font-family="Arial">🔗 entered_by (FK)</text>
  </g>
  
  <!-- Transactions Table -->
  <g transform="translate(300, 280)">
    <rect width="220" height="240" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="220" height="30" fill="#2c3e50" rx="5"/>
    <text x="110" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Transactions</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">transaction_number</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">🔗 transaction_type_id (FK)</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">🔗 customer_id (FK)</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">🔗 location_id (FK)</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">🔗 currency_id (FK)</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">amount</text>
    <text x="10" y="150" fill="white" font-size="10" font-family="Arial">exchange_rate</text>
    <text x="10" y="165" fill="white" font-size="10" font-family="Arial">commission_rate</text>
    <text x="10" y="180" fill="white" font-size="10" font-family="Arial">total_amount</text>
    <text x="10" y="195" fill="white" font-size="10" font-family="Arial">status</text>
    <text x="10" y="210" fill="white" font-size="10" font-family="Arial">🔗 created_by (FK)</text>
    <text x="10" y="225" fill="white" font-size="10" font-family="Arial">transaction_date</text>
  </g>
  
  <!-- Transaction Entries Table -->
  <g transform="translate(560, 280)">
    <rect width="200" height="165" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Transaction Entries</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">🔗 transaction_id (FK)</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">account_type</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">account_id</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">🔗 currency_id (FK)</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">debit_amount</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">credit_amount</text>
    <text x="10" y="150" fill="white" font-size="10" font-family="Arial">description</text>
  </g>
  
  <!-- Customer Balances Table -->
  <g transform="translate(50, 540)">
    <rect width="200" height="120" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Customer Balances</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">🔗 customer_id (FK)</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">🔗 currency_id (FK)</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">balance</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">last_updated</text>
  </g>
  
  <!-- Company Balances Table -->
  <g transform="translate(300, 540)">
    <rect width="200" height="120" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Company Balances</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">🔗 location_id (FK)</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">🔗 currency_id (FK)</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">balance</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">last_updated</text>
  </g>
  
  <!-- Transaction Types Table -->
  <g transform="translate(800, 280)">
    <rect width="180" height="120" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="180" height="30" fill="#2c3e50" rx="5"/>
    <text x="90" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Transaction Types</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">code</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">name</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">description</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">is_active</text>
  </g>
  
  <!-- Couriers Table -->
  <g transform="translate(1020, 280)">
    <rect width="160" height="105" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="160" height="30" fill="#2c3e50" rx="5"/>
    <text x="80" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Couriers</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">name</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">phone</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">is_active</text>
  </g>
  
  <!-- Audit Log Table -->
  <g transform="translate(550, 540)">
    <rect width="200" height="150" fill="url(#tableGrad)" stroke="#2980b9" stroke-width="2" rx="5" filter="url(#shadow)"/>
    <rect width="200" height="30" fill="#2c3e50" rx="5"/>
    <text x="100" y="20" text-anchor="middle" fill="white" font-size="14" font-weight="bold" font-family="Arial">Audit Log</text>
    <text x="10" y="45" fill="white" font-size="10" font-family="Arial">🔑 id (PK)</text>
    <text x="10" y="60" fill="white" font-size="10" font-family="Arial">table_name</text>
    <text x="10" y="75" fill="white" font-size="10" font-family="Arial">record_id</text>
    <text x="10" y="90" fill="white" font-size="10" font-family="Arial">action</text>
    <text x="10" y="105" fill="white" font-size="10" font-family="Arial">old_values</text>
    <text x="10" y="120" fill="white" font-size="10" font-family="Arial">new_values</text>
    <text x="10" y="135" fill="white" font-size="10" font-family="Arial">🔗 user_id (FK)</text>
  </g>
  
  <!-- Relationships -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c"/>
    </marker>
  </defs>
  
  <!-- Users to Locations -->
  <path d="M 250 160 L 520 160" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="385" y="155" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">manages</text>
  
  <!-- Users to Customers -->
  <path d="M 150 260 L 150 280" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="120" y="275" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">creates</text>
  
  <!-- Customers to Transactions -->
  <path d="M 250 377 L 300 377" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="275" y="372" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">has</text>
  
  <!-- Transactions to Transaction Entries -->
  <path d="M 520 377 L 560 377" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="540" y="372" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">has</text>
  
  <!-- Customers to Customer Balances -->
  <path d="M 150 475 L 150 540" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="120" y="510" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">has</text>
  
  <!-- Locations to Company Balances -->
  <path d="M 400 230 L 400 540" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="370" y="385" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">has</text>
  
  <!-- Currencies to Exchange Rates -->
  <path d="M 930 147 L 980 147" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="955" y="142" text-anchor="middle" fill="#e74c3c" font-size="9" font-family="Arial">rates</text>
  
  <!-- Legend -->
  <rect x="50" y="720" width="300" height="120" fill="white" stroke="#bdc3c7" stroke-width="1" rx="5" filter="url(#shadow)"/>
  <text x="200" y="740" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold" font-family="Arial">Legend</text>
  <text x="60" y="760" fill="#2c3e50" font-size="12" font-family="Arial">🔑 Primary Key (PK)</text>
  <text x="60" y="780" fill="#2c3e50" font-size="12" font-family="Arial">🔗 Foreign Key (FK)</text>
  <path d="M 60 800 L 100 800" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="110" y="805" fill="#2c3e50" font-size="12" font-family="Arial">One-to-Many Relationship</text>
  <text x="60" y="825" fill="#2c3e50" font-size="10" font-family="Arial">Double-entry bookkeeping principles applied</text>
</svg>
