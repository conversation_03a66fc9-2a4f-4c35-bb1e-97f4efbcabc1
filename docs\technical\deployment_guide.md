# 🚀 راهنمای استقرار (Windows Server + Nginx + Gunicorn + Docker)

این راهنما مراحل استقرار سیستم Arena Doviz را روی Windows Server توضیح می‌دهد. زیرساخت پیشنهادی: Nginx به عنوان Reverse Proxy، Gunicorn به عنوان WSGI، PostgreSQL، و Docker برای کانتینرسازی.

## پیش‌نیازها
- Windows Server 2019/2022 یا Windows 10/11 برای محیط آزمایشی
- دسترسی Administrator
- نصب: Docker Desktop/Engine، Git، OpenSSL، Nginx (نسخه ویندوز یا پشت IIS با ARR)
- پایگاه‌داده PostgreSQL 15+

## پیکربندی محیط
1. ایجاد کاربر سرویس با حداقل دسترسی
2. تنظیم متغیرهای محیطی (Secrets) در فایل `.env`:
   - DATABASE_URL
   - SECRET_KEY
   - ALLOWED_HOSTS
   - REDIS_URL (در صورت استفاده)
   - DEBUG=false در محیط تولید

## ساخت ایمیج‌ها و اجرای سرویس‌ها
- اگر docker-compose موجود است:
  - `docker-compose -f deployment/docker-compose.yml up -d --build`
- اگر نیست:
  1. ساخت ایمیج: `docker build -t arena-backend:prod -f deployment/Dockerfile .`
  2. اجرای پایگاه‌داده (درون یا بیرون Docker)
  3. اجرای Gunicorn:
     - `docker run --env-file .env -p 8000:8000 arena-backend:prod gunicorn config.wsgi:application -b 0.0.0.0:8000 --workers 3 --timeout 120`

## تنظیم Nginx (نمونه)
```
server {
    listen 80;
    server_name arena.example.com;

    client_max_body_size 20m;

    location /static/ {
        alias /var/www/arena/static/;
    }

    location /media/ {
        alias /var/www/arena/media/;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

برای HTTPS از certbot یا گواهی سازمانی استفاده کنید و بلاک 443 را اضافه نمایید.

## مهاجرت دیتابیس و جمع‌آوری فایل‌های استاتیک
- `python manage.py migrate`
- `python manage.py collectstatic --noinput`

اگر داخل کانتینر اجرا می‌شود:
- `docker exec -it <container> python manage.py migrate`
- `docker exec -it <container> python manage.py collectstatic --noinput`

## پشتیبان‌گیری خودکار
- اسکریپت نمونه (Windows Task Scheduler):
```
powershell -Command "pg_dump -h localhost -U postgres -d exchange_db | gzip > C:\\backups\\exchange_db_$(Get-Date -Format yyyyMMdd).sql.gz"
```
- نگهداری 30 نسخه آخر؛ انتقال به مسیر امن شبکه‌ای

## مانیتورینگ و لاگ
- فعال‌سازی لاگ سطح INFO برای اپلیکیشن، ERROR برای Django
- چرخش لاگ‌ها (logrotate معادل روی ویندوز یا ابزارهای سوم)
- مانیتورینگ سلامت سرویس با URL `/healthz`

## نکات امنیتی
- غیرفعال کردن DEBUG در تولید
- محدودسازی ALLOWED_HOSTS
- استفاده از HTTPS اجباری
- تنظیمات CSRF و SECURE_* در Django
- جداسازی شبکه Docker و عدم اکسپوز مستقیم دیتابیس

