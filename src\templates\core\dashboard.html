{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h2 mb-4">
            <i class="bi bi-speedometer2"></i>
            {% trans "Dashboard" %}
        </h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Total Customers" %}</h5>
                        <h2 class="mb-0" id="total-customers">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Today's Transactions" %}</h5>
                        <h2 class="mb-0" id="today-transactions">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Active Locations" %}</h5>
                        <h2 class="mb-0" id="active-locations">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-geo-alt fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Pending Approvals" %}</h5>
                        <h2 class="mb-0" id="pending-approvals">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Daily Transaction Volume" %}
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="loadChartData(7)">7D</button>
                    <button type="button" class="btn btn-outline-primary active" onclick="loadChartData(30)">30D</button>
                    <button type="button" class="btn btn-outline-primary" onclick="loadChartData(90)">90D</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="100"></canvas>
                <div id="transactionChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    {% trans "Currency Distribution" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="currencyChart" height="200"></canvas>
                <div id="currencyChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Charts Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {% trans "Profit Analysis" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="profitChart" height="150"></canvas>
                <div id="profitChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart-fill"></i>
                    {% trans "Transaction Status" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="150"></canvas>
                <div id="statusChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.can_manage_users %}
<!-- Admin Only Charts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-building"></i>
                    {% trans "Location Performance" %}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="locationChart" height="80"></canvas>
                <div id="locationChartLoading" class="text-center py-4" style="display: none;">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Recent Transactions" %}
                </h5>
                <a href="{% url 'transactions_web:list' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Exchange Rates -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-currency-exchange"></i>
                    {% trans "Current Exchange Rates" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row" id="exchange-rates">
                    <div class="col-12 text-center text-muted">
                        {% trans "Loading exchange rates..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Utility functions
function getAuthToken() {
    // This would typically get the token from localStorage or a cookie
    // For now, we'll assume it's available in a meta tag or global variable
    return window.authToken || $('meta[name="auth-token"]').attr('content') || '';
}

function getStatusClass(status) {
    const statusClasses = {
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

// Global chart instances
let charts = {};

$(document).ready(function() {
    // Initialize dashboard
    loadDashboardData();
    loadChartData(30); // Load 30 days by default

    // Refresh data every 5 minutes
    setInterval(function() {
        loadDashboardData();
        loadChartData(30);
    }, 300000);
});

function loadDashboardData() {
    // Load summary statistics
    loadSummaryStats();
    
    // Load recent transactions
    loadRecentTransactions();
    
    // Load exchange rates
    loadExchangeRates();
}

function loadSummaryStats() {
    // Load customer statistics
    $.ajax({
        url: '/api/v1/customers/customers/stats/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            $('#total-customers').text(data.total_customers || 0);
        },
        error: function() {
            $('#total-customers').text('Error');
        }
    });

    // Load transaction statistics
    $.ajax({
        url: '/api/v1/transactions/transactions/stats/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            $('#today-transactions').text(data.total_volume_today || 0);
            $('#pending-approvals').text(data.pending_transactions || 0);
        },
        error: function() {
            $('#today-transactions').text('Error');
            $('#pending-approvals').text('Error');
        }
    });

    // Load location statistics
    $.ajax({
        url: '/api/v1/locations/locations/stats/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            $('#active-locations').text(data.active_locations || 0);
        },
        error: function() {
            $('#active-locations').text('Error');
        }
    });
}

function loadRecentTransactions() {
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        data: {
            ordering: '-created_at',
            limit: 10
        },
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            const tbody = $('#recent-transactions-table tbody');
            tbody.empty();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(tx) {
                    const statusClass = getStatusClass(tx.status);
                    const formattedDate = new Date(tx.created_at).toLocaleString();

                    const row = `
                        <tr>
                            <td><code>${tx.transaction_number}</code></td>
                            <td>${tx.customer_name}</td>
                            <td>${tx.display_amount}</td>
                            <td><span class="badge bg-${statusClass}">${tx.status_display}</span></td>
                            <td>${formattedDate}</td>
                            <td>
                                <a href="/transactions/${tx.id}/" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                tbody.append(`
                    <tr>
                        <td colspan="6" class="text-center text-muted">
                            {% trans "No recent transactions found" %}
                        </td>
                    </tr>
                `);
            }
        },
        error: function() {
            const tbody = $('#recent-transactions-table tbody');
            tbody.empty();
            tbody.append(`
                <tr>
                    <td colspan="6" class="text-center text-danger">
                        {% trans "Error loading transactions" %}
                    </td>
                </tr>
            `);
        }
    });
}

function loadExchangeRates() {
    $.ajax({
        url: '/api/v1/currencies/rates/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            const container = $('#exchange-rates');
            container.empty();

            if (data.results && data.results.length > 0) {
                data.results.forEach(function(rate) {
                    const card = `
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <h6 class="card-title">${rate.from_currency_code} → ${rate.to_currency_code}</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Buy</small>
                                            <div class="fw-bold text-success">${rate.buy_rate || 'N/A'}</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Sell</small>
                                            <div class="fw-bold text-danger">${rate.sell_rate || 'N/A'}</div>
                                        </div>
                                    </div>
                                    <small class="text-muted">${rate.location_name}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    container.append(card);
                });
            } else {
                container.append(`
                    <div class="col-12 text-center text-muted">
                        {% trans "No exchange rates available" %}
                    </div>
                `);
            }
        },
        error: function() {
            const container = $('#exchange-rates');
            container.empty();
            container.append(`
                <div class="col-12 text-center text-danger">
                    {% trans "Error loading exchange rates" %}
                </div>
            `);
        }
    });
}

function loadChartData(days = 30) {
    // Update active button
    $('.btn-group button').removeClass('active');
    $(`.btn-group button:contains('${days}D')`).addClass('active');

    // Show loading indicators
    showChartLoading(true);

    // Make API request for chart data
    ArenaDoviz.api.request('GET', `core/dashboard/chart_data/?days=${days}`)
        .then(data => {
            initializeCharts(data);
            showChartLoading(false);
        })
        .catch(error => {
            console.error('Error loading chart data:', error);
            showChartLoading(false);
            showAlert('error', '{% trans "Failed to load chart data" %}');
        });
}

function showChartLoading(show) {
    const loadingElements = [
        '#transactionChartLoading',
        '#currencyChartLoading',
        '#profitChartLoading',
        '#statusChartLoading',
        '#locationChartLoading'
    ];

    const chartElements = [
        '#transactionChart',
        '#currencyChart',
        '#profitChart',
        '#statusChart',
        '#locationChart'
    ];

    if (show) {
        loadingElements.forEach(el => $(el).show());
        chartElements.forEach(el => $(el).hide());
    } else {
        loadingElements.forEach(el => $(el).hide());
        chartElements.forEach(el => $(el).show());
    }
}

function initializeCharts(data) {
    // Use the enhanced chart utilities
    ArenaDovizCharts.init(data);
}
</script>
{% endblock %}
