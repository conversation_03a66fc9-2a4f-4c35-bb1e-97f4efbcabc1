<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="startGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decisionGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f39c12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e67e22;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="endGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1400" height="1200" fill="#f8f9fa"/>

  <!-- Title -->
  <rect x="0" y="0" width="1400" height="50" fill="#2c3e50"/>
  <text x="700" y="30" text-anchor="middle" fill="white" font-size="20" font-weight="bold" font-family="Arial">
    Arena Doviz - Business Process Flow
  </text>

  <!-- Start -->
  <ellipse cx="700" cy="100" rx="80" ry="30" fill="url(#startGrad)" stroke="#229954" stroke-width="2" filter="url(#shadow)"/>
  <text x="700" y="105" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">START</text>

  <!-- Staff Login -->
  <rect x="620" y="150" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="700" y="170" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Staff Login</text>
  <text x="700" y="185" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Authentication</text>

  <!-- Main Menu Decision -->
  <polygon points="700,230 750,260 700,290 650,260" fill="url(#decisionGrad)" stroke="#e67e22" stroke-width="2" filter="url(#shadow)"/>
  <text x="700" y="255" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Select</text>
  <text x="700" y="270" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Operation</text>

  <!-- Customer Management Branch -->
  <rect x="100" y="350" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="180" y="370" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Customer</text>
  <text x="180" y="385" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Management</text>

  <!-- Transaction Processing Branch -->
  <rect x="320" y="350" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="400" y="370" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Transaction</text>
  <text x="400" y="385" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Processing</text>

  <!-- Exchange Rate Management -->
  <rect x="540" y="350" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="620" y="370" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Exchange Rate</text>
  <text x="620" y="385" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Management</text>

  <!-- Reporting -->
  <rect x="760" y="350" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="840" y="370" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Report</text>
  <text x="840" y="385" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Generation</text>

  <!-- Balance Management -->
  <rect x="980" y="350" width="160" height="50" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="1060" y="370" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Balance</text>
  <text x="1060" y="385" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">Management</text>

  <!-- Transaction Type Decision -->
  <polygon points="400,450 470,480 400,510 330,480" fill="url(#decisionGrad)" stroke="#e67e22" stroke-width="2" filter="url(#shadow)"/>
  <text x="400" y="475" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Transaction</text>
  <text x="400" y="490" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Type?</text>

  <!-- Buy Currency -->
  <rect x="150" y="550" width="120" height="40" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="210" y="575" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Buy Currency</text>

  <!-- Sell Currency -->
  <rect x="290" y="550" width="120" height="40" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="350" y="575" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Sell Currency</text>

  <!-- Internal Transfer -->
  <rect x="430" y="550" width="120" height="40" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="490" y="575" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Internal Transfer</text>

  <!-- Physical Delivery -->
  <rect x="570" y="550" width="120" height="40" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="630" y="575" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Physical Delivery</text>
  <!-- WhatsApp Desktop Flow Note -->
  <text x="980" y="200" fill="#2c3e50" font-size="10" font-family="Arial">WhatsApp Desktop: open → preview → approve → send</text>


  <!-- Balance Update -->
  <rect x="300" y="650" width="140" height="40" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" rx="8" filter="url(#shadow)"/>
  <text x="370" y="675" text-anchor="middle" fill="white" font-size="10" font-weight="bold" font-family="Arial">Update Balances</text>

  <!-- Transaction Complete -->
  <ellipse cx="700" cy="900" rx="100" ry="30" fill="url(#endGrad)" stroke="#c0392b" stroke-width="2" filter="url(#shadow)"/>
  <text x="700" y="905" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Transaction Complete</text>

  <!-- End -->
  <ellipse cx="700" cy="1100" rx="60" ry="25" fill="url(#endGrad)" stroke="#c0392b" stroke-width="2" filter="url(#shadow)"/>
  <text x="700" y="1105" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">END</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- Main Flow -->
  <path d="M 700 130 L 700 150" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 700 200 L 700 230" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Branch to operations -->
  <path d="M 650 260 L 180 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="415" y="305" fill="#2c3e50" font-size="9" font-family="Arial">Customer Mgmt</text>

  <path d="M 670 280 L 400 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="535" y="315" fill="#2c3e50" font-size="9" font-family="Arial">Transaction</text>

  <!-- Transaction processing flow -->
  <path d="M 400 400 L 400 450" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Transaction type branches -->
  <path d="M 330 480 L 210 550" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="270" y="515" fill="#2c3e50" font-size="9" font-family="Arial">Buy</text>

  <path d="M 370 500 L 350 550" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="360" y="525" fill="#2c3e50" font-size="9" font-family="Arial">Sell</text>

  <path d="M 400 510 L 490 550" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="445" y="530" fill="#2c3e50" font-size="9" font-family="Arial">Transfer</text>

  <path d="M 470 480 L 630 550" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="550" y="515" fill="#2c3e50" font-size="9" font-family="Arial">Delivery</text>

  <!-- Balance flow -->
  <path d="M 210 590 L 370 650" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 350 590 L 370 650" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 490 590 L 370 650" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 630 590 L 370 650" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Convergence to completion -->
  <path d="M 370 690 L 650 900" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>

  <!-- End flow -->
  <path d="M 700 930 L 700 1075" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>

  <!-- Legend -->
  <rect x="1050" y="450" width="300" height="200" fill="white" stroke="#bdc3c7" stroke-width="1" rx="5" filter="url(#shadow)"/>
  <text x="1200" y="470" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold" font-family="Arial">Process Legend</text>

  <ellipse cx="1080" cy="490" rx="25" ry="12" fill="url(#startGrad)" stroke="#229954" stroke-width="1"/>
  <text x="1115" y="495" fill="#2c3e50" font-size="10" font-family="Arial">Start/End</text>

  <rect x="1070" y="510" width="40" height="20" fill="url(#processGrad)" stroke="#2980b9" stroke-width="1" rx="3"/>
  <text x="1115" y="525" fill="#2c3e50" font-size="10" font-family="Arial">Process</text>

  <polygon points="1080,540 1100,550 1080,560 1060,550" fill="url(#decisionGrad)" stroke="#e67e22" stroke-width="1"/>
  <text x="1115" y="555" fill="#2c3e50" font-size="10" font-family="Arial">Decision</text>

  <text x="1070" y="580" fill="#2c3e50" font-size="12" font-weight="bold" font-family="Arial">Key Features:</text>
  <text x="1070" y="600" fill="#2c3e50" font-size="10" font-family="Arial">• Real-time balance updates</text>
  <text x="1070" y="615" fill="#2c3e50" font-size="10" font-family="Arial">• Multi-location support</text>
  <text x="1070" y="630" fill="#2c3e50" font-size="10" font-family="Arial">• Comprehensive audit trail</text>
</svg>
