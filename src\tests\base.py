"""
Base test classes and utilities for Arena Doviz test suite.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.customers.models import Customer
from apps.transactions.models import TransactionType
import logging

# Disable logging during tests
logging.disable(logging.CRITICAL)

User = get_user_model()


class BaseTestCase(TestCase):
    """Base test case with common setup and utilities."""
    
    @classmethod
    def setUpTestData(cls):
        """Set up test data that will be used across all test methods."""
        # Create test location
        cls.location = Location.objects.create(
            name='Test Location',
            code='TEST',
            address='123 Test Street',
            phone_number='+**********',
            is_active=True
        )
        
        # Create test currencies
        cls.usd = Currency.objects.create(
            code='USD',
            name='US Dollar',
            symbol='$',
            decimal_places=2,
            is_active=True
        )
        
        cls.aed = Currency.objects.create(
            code='AED',
            name='UAE Dirham',
            symbol='د.إ',
            decimal_places=2,
            is_active=True
        )
        
        cls.irr = Currency.objects.create(
            code='IRR',
            name='Iranian Rial',
            symbol='﷼',
            decimal_places=0,
            is_active=True
        )
        
        # Create test users with different roles
        cls.admin_user = User.objects.create_user(
            username='admin_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User',
            role=User.Role.ADMIN,
            location=cls.location,
            is_active=True
        )
        
        cls.accountant_user = User.objects.create_user(
            username='accountant_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='Accountant',
            last_name='User',
            role=User.Role.ACCOUNTANT,
            location=cls.location,
            is_active=True
        )
        
        cls.employee_user = User.objects.create_user(
            username='employee_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='Employee',
            last_name='User',
            role=User.Role.BRANCH_EMPLOYEE,
            location=cls.location,
            is_active=True
        )
        
        cls.viewer_user = User.objects.create_user(
            username='viewer_test',
            email='<EMAIL>',
            password='testpass123',
            first_name='Viewer',
            last_name='User',
            role=User.Role.VIEWER,
            location=cls.location,
            is_active=True
        )
        
        # Create test customer
        cls.customer = Customer.objects.create(
            customer_type=Customer.CustomerType.INDIVIDUAL,
            first_name='John',
            last_name='Doe',
            email='<EMAIL>',
            phone_number='+**********',
            preferred_currency=cls.usd,
            preferred_location=cls.location
        )
        
        # Create test transaction type
        cls.transaction_type = TransactionType.objects.create(
            name='Currency Exchange',
            code='EXCHANGE',
            description='Currency exchange transaction',
            is_active=True
        )
    
    def create_test_user(self, username, role=User.Role.BRANCH_EMPLOYEE, **kwargs):
        """Create a test user with specified role."""
        defaults = {
            'email': f'{username}@test.com',
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'User',
            'location': self.location,
            'is_active': True
        }
        defaults.update(kwargs)
        
        return User.objects.create_user(
            username=username,
            role=role,
            **defaults
        )
    
    def create_test_customer(self, customer_code, **kwargs):
        """Create a test customer."""
        defaults = {
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'first_name': 'Test',
            'last_name': 'Customer',
            'email': f'{customer_code.lower()}@test.com',
            'phone_number': '+**********',
            'preferred_location': self.location,
            'status': Customer.Status.ACTIVE
        }
        defaults.update(kwargs)

        return Customer.objects.create(
            customer_code=customer_code,
            **defaults
        )
    
    def assertDecimalEqual(self, first, second, places=6):
        """Assert that two decimal values are equal within specified decimal places."""
        if isinstance(first, (int, float)):
            first = Decimal(str(first))
        if isinstance(second, (int, float)):
            second = Decimal(str(second))
        
        self.assertEqual(
            round(first, places),
            round(second, places),
            f"Decimal values not equal: {first} != {second}"
        )


class BaseAPITestCase(APITestCase, BaseTestCase):
    """Base API test case with authentication and common API utilities."""
    
    def setUp(self):
        """Set up API client and authentication."""
        super().setUp()
        self.client = APIClient()
        self.authenticate_as(self.admin_user)
    
    def authenticate_as(self, user):
        """Authenticate API client as specified user using JWT."""
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        self.current_user = user
    
    def unauthenticate(self):
        """Remove authentication from API client."""
        self.client.credentials()
        self.current_user = None
    
    def assertAPISuccess(self, response, status_code=200):
        """Assert that API response is successful."""
        self.assertEqual(
            response.status_code, 
            status_code,
            f"Expected status {status_code}, got {response.status_code}. "
            f"Response: {getattr(response, 'data', response.content)}"
        )
    
    def assertAPIError(self, response, status_code=400):
        """Assert that API response is an error."""
        self.assertGreaterEqual(
            response.status_code,
            status_code,
            f"Expected error status >= {status_code}, got {response.status_code}. "
            f"Response: {getattr(response, 'data', response.content)}"
        )
    
    def assertAPIPermissionDenied(self, response):
        """Assert that API response is permission denied."""
        self.assertIn(
            response.status_code,
            [401, 403],
            f"Expected permission denied (401/403), got {response.status_code}. "
            f"Response: {getattr(response, 'data', response.content)}"
        )
    
    def get_api_url(self, endpoint):
        """Get full API URL for endpoint."""
        if not endpoint.startswith('/'):
            endpoint = '/' + endpoint
        if not endpoint.startswith('/api/v1/'):
            endpoint = '/api/v1' + endpoint
        return endpoint


class BaseTransactionTestCase(TransactionTestCase):
    """Base test case for tests that require database transactions."""

    def setUp(self):
        """Set up test data for each test method."""
        # Create test location
        self.location = Location.objects.create(
            name='Test Location',
            code='TEST',
            address='123 Test Street',
            phone_number='+**********',
            is_active=True
        )

        # Create test currencies
        self.usd = Currency.objects.create(
            code='USD',
            name='US Dollar',
            symbol='$',
            decimal_places=2,
            is_active=True
        )

        self.aed = Currency.objects.create(
            code='AED',
            name='UAE Dirham',
            symbol='د.إ',
            decimal_places=2,
            is_active=True
        )

        self.irr = Currency.objects.create(
            code='IRR',
            name='Iranian Rial',
            symbol='﷼',
            decimal_places=0,
            is_active=True
        )

        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.ADMIN,
            location=self.location,
            is_active=True
        )

        self.accountant_user = User.objects.create_user(
            username='accountant',
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.ACCOUNTANT,
            location=self.location,
            is_active=True
        )

        self.employee_user = User.objects.create_user(
            username='employee',
            email='<EMAIL>',
            password='testpass123',
            role=User.Role.BRANCH_EMPLOYEE,
            location=self.location,
            is_active=True
        )

        # Create test customer
        self.customer = Customer.objects.create(
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            phone_number='+**********',
            customer_type=Customer.CustomerType.INDIVIDUAL,
            preferred_currency=self.usd,
            preferred_location=self.location
        )

        # Create test transaction type
        self.transaction_type = TransactionType.objects.create(
            name='Currency Exchange',
            code='EXCHANGE',
            description='Currency exchange transaction',
            is_active=True
        )


class TestDataMixin:
    """Mixin providing methods to create test data."""
    
    def create_exchange_rate(self, from_currency, to_currency, rate, location=None):
        """Create an exchange rate."""
        from apps.currencies.models import ExchangeRate
        
        return ExchangeRate.objects.create(
            from_currency=from_currency,
            to_currency=to_currency,
            rate=Decimal(str(rate)),
            location=location or self.location,
            is_active=True
        )
    
    def create_transaction(self, **kwargs):
        """Create a test transaction."""
        from apps.transactions.models import Transaction
        
        defaults = {
            'customer': self.customer,
            'location': self.location,
            'transaction_type': self.transaction_type,
            'from_currency': self.usd,
            'to_currency': self.aed,
            'from_amount': Decimal('1000.00'),
            'to_amount': Decimal('3670.00'),
            'exchange_rate': Decimal('3.67'),
            'commission_amount': Decimal('10.00'),
            'commission_currency': self.usd,
            'status': Transaction.Status.DRAFT,
            'created_by': self.admin_user
        }
        defaults.update(kwargs)
        
        return Transaction.objects.create(**defaults)
    
    def create_commission_rule(self, **kwargs):
        """Create a test commission rule."""
        from apps.transactions.commission_models import CommissionRule
        
        defaults = {
            'name': 'Test Commission Rule',
            'commission_type': CommissionRule.CommissionType.PERCENTAGE,
            'percentage_rate': Decimal('1.5'),
            'is_active': True,
            'priority': 100,
            'created_by': self.admin_user
        }
        defaults.update(kwargs)
        
        return CommissionRule.objects.create(**defaults)


class MockMixin:
    """Mixin providing mock utilities for testing."""
    
    def mock_whatsapp_integration(self):
        """Mock WhatsApp integration for testing."""
        # This would be implemented when WhatsApp integration is added
        pass
    
    def mock_external_api(self, api_name, response_data):
        """Mock external API responses."""
        # This would be implemented for external API testing
        pass


# Test utilities
def create_test_file(filename='test.txt', content='Test file content'):
    """Create a test file for upload testing."""
    from django.core.files.uploadedfile import SimpleUploadedFile
    return SimpleUploadedFile(filename, content.encode('utf-8'))


def assert_audit_log_created(test_case, user, action, model_name=None, object_id=None):
    """Assert that an audit log entry was created."""
    from apps.accounts.models import AuditLog

    logs = AuditLog.objects.filter(
        user=user,
        action=action
    )

    if model_name:
        logs = logs.filter(model_name=model_name)

    if object_id:
        logs = logs.filter(object_id=object_id)
    
    test_case.assertTrue(
        logs.exists(),
        f"Expected audit log not found: user={user}, action={action}, "
        f"model_name={model_name}, object_id={object_id}"
    )
    
    return logs.first()


def assert_balance_entry_created(test_case, transaction, entry_type):
    """Assert that a balance entry was created for a transaction."""
    from apps.transactions.models import BalanceEntry
    
    entry = BalanceEntry.objects.filter(
        transaction=transaction,
        entry_type=entry_type
    ).first()
    
    test_case.assertIsNotNone(
        entry,
        f"Expected balance entry not found: transaction={transaction}, entry_type={entry_type}"
    )
    
    return entry
