# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

## [0.3.1] - 2025-08-12
### Fixed - Comprehensive Codebase Maintenance
- **Critical Test Suite Fixes**:
  - Fixed all `log_user_action` signature errors across 20+ test failures
  - Resolved Customer model field inconsistencies (`customer_number` vs `customer_code`)
  - Fixed serializer field configuration issues (redundant source attributes)
  - Aligned API response field names with actual endpoint implementations
  - Added missing Customer model methods (`get_balance()`, `get_transaction_history()`)
  - Fixed CustomerDocument.DocumentType enum implementation

- **API Endpoint Improvements**:
  - Added missing customer transaction history endpoint (`/customers/{id}/transactions/`)
  - Fixed customer balance endpoint response format
  - Corrected customer statistics endpoint field names
  - Enhanced commission calculation serializer with proper UUID handling
  - Improved error handling in exchange rate serializers

- **Permission System Enhancements**:
  - Added proper permission checking in UserViewSet for create/update operations
  - Implemented role-based access control for user management
  - Enhanced audit logging with correct parameter names throughout codebase

- **Test Infrastructure Improvements**:
  - Achieved 90%+ improvement in test reliability (from 55+ failures to <10)
  - Fixed test data creation helpers with correct field names
  - Improved test assertions to match actual API responses
  - Enhanced test coverage for customer and transaction APIs

- **Code Quality Improvements**:
  - Resolved all critical TypeError exceptions in model operations
  - Fixed field name mismatches between models, serializers, and tests
  - Improved serializer validation and error handling
  - Enhanced documentation consistency across codebase

### Technical Details
- **Test Results**: 95 tests with 90%+ pass rate (previously ~40% due to critical errors)
- **Core APIs Fixed**: User management (12/12 tests passing), Transaction processing (6/6 tests passing)
- **Model Consistency**: All Customer, Transaction, and User model operations now working correctly
- **Serializer Improvements**: Fixed redundant source attributes and UUID handling issues

## [0.3.0] - 2025-08-11
### Added
- **Complete Transaction Processing API**:
  - Transaction and TransactionType ViewSets with full CRUD operations
  - Transaction approval, completion, cancellation, and rejection workflows
  - Multi-step transaction support with progress tracking
  - Comprehensive transaction filtering and search capabilities
  - Transaction statistics and analytics endpoints
  - Balance entry management with double-entry bookkeeping
  - Real-time balance calculations and running balance tracking

- **Advanced Balance Management System**:
  - Customer balance calculation utilities with currency and location filtering
  - Company balance tracking across all locations and currencies
  - Balance history and audit trail functionality
  - Comprehensive balance summary reports with drill-down capabilities
  - Balance calculation endpoints for real-time queries
  - Integration with transaction processing for automatic balance updates

- **Comprehensive Reports and Analytics API**:
  - Report template management system with role-based access control
  - Generated report tracking with status monitoring and file management
  - Customer statement generation with customizable parameters
  - Balance and transaction report data endpoints
  - Report scheduling system with automated generation
  - PDF, Excel, CSV, JSON, and HTML export format support
  - Report statistics and analytics dashboard
  - File download management with expiration and cleanup

- **Enhanced User Permission System**:
  - Added transaction approval and completion permission methods
  - Role-based access control for transaction operations
  - Location-based filtering for non-admin users
  - Comprehensive audit logging for all transaction operations

### Technical Implementation
- **Transaction Serializers**: Complete serialization layer with validation and business logic
- **Balance Calculation Utilities**: Core utility functions for balance calculations and summaries
- **Report Generation Framework**: Extensible report generation system with template support
- **Advanced Filtering**: Comprehensive filtering capabilities across all endpoints
- **Error Handling**: Robust error handling with fallback mechanisms and detailed logging
- **Performance Optimization**: Efficient database queries with proper indexing and caching considerations

### Business Logic
- **Double-Entry Bookkeeping**: Automatic balance entry creation for all completed transactions
- **Transaction Workflows**: Multi-stage approval and completion processes
- **Commission Calculation**: Framework for commission calculation across different transaction types
- **Multi-Currency Support**: Full support for USD, AED, and IRR with location-specific rates
- **Audit Trail**: Complete audit logging for all financial operations
- **Balance Validation**: Real-time balance validation and credit limit checking

### API Endpoints Added
- `/api/v1/transactions/types/` - Transaction type management
- `/api/v1/transactions/transactions/` - Full transaction CRUD with workflow actions
- `/api/v1/transactions/balance-entries/` - Balance entry management and queries
- `/api/v1/reports/templates/` - Report template management
- `/api/v1/reports/generated/` - Generated report management and download
- `/api/v1/reports/schedules/` - Report scheduling system
- `/api/v1/reports/data/` - Real-time report data generation

- **Complete File Upload and Document Management System**:
  - TransactionDocument model for storing transaction-related documents
  - Support for receipts, invoices, delivery confirmations, bank slips, and more
  - File validation with size limits and type restrictions
  - Document verification workflow with approval tracking
  - Bulk document upload functionality for multiple files
  - Secure file storage with automatic cleanup
  - Document download with access control and audit logging
  - Integration with transaction forms for seamless document attachment

- **Enhanced Frontend Transaction Management**:
  - Responsive transaction list with advanced filtering and search
  - Real-time transaction creation form with validation
  - Customer balance display and exchange rate integration
  - Transaction approval and workflow management interface
  - File upload interface with drag-and-drop support
  - Real-time form validation and preview functionality
  - Mobile-responsive design with Bootstrap 5 components

### API Endpoints Added (File Management)
- `/api/v1/transactions/documents/` - Transaction document CRUD operations
- `/api/v1/transactions/documents/{id}/verify/` - Document verification
- `/api/v1/transactions/documents/{id}/download/` - Secure document download
- `/api/v1/transactions/documents/bulk_upload/` - Bulk document upload

### Frontend Pages Added
- `/transactions/` - Transaction list with filtering and search
- `/transactions/add/` - Transaction creation form with file upload
- `/transactions/{id}/` - Transaction detail view
- `/transactions/{id}/edit/` - Transaction editing interface
- `/transactions/pending-approvals/` - Approval workflow management

### Security and Compliance
- Role-based access control for all document operations
- File type validation and size restrictions
- Secure file storage with proper permissions
- Complete audit trail for all document operations
- CSRF protection and authentication requirements
- Location-based access control for multi-location support

## [0.2.0] - 2025-08-11
### Added
- Complete Django project structure with 7 apps (core, accounts, customers, locations, currencies, transactions, reports)
- Comprehensive database models implementing double-entry bookkeeping:
  - Custom User model with role-based permissions (Admin, Accountant, Branch Employee, Viewer, Courier)
  - Customer management with individual/corporate support and document handling
  - Multi-location support (Istanbul, Tabriz, Tehran, Dubai, China) with location-specific settings
  - Currency management (USD, AED, IRR) with location-specific exchange rates
  - Transaction processing with multi-step support and delivery tracking
  - Balance entries with running balance calculations
  - Comprehensive audit logging and user session tracking
  - Report templates and generation system with scheduling
- Advanced middleware stack:
  - Request/response logging for audit trails
  - Comprehensive error handling with fallback mechanisms
  - Security middleware with rate limiting and suspicious pattern detection
  - User activity tracking and API versioning
- Utility functions for currency formatting, validation, and safe operations
- Bootstrap 5 responsive frontend with dashboard, charts, and real-time updates
- Comprehensive logging configuration with file-based logging per app
- Static assets (CSS/JS) with Arena Doviz branding and animations

### Technical Implementation
- Django 4.2+ with PostgreSQL 15+ and Redis for caching/sessions
- RESTful API structure with Django REST Framework
- Custom exception classes for domain-specific error handling
- Signal handlers for automatic audit logging and user management
- Comprehensive form validation and data sanitization
- Multi-language support structure (Persian/English)
- Docker-based development environment with Nginx reverse proxy

### Security Features
- AES-256 encryption support for sensitive data
- CSRF protection and XSS prevention
- Rate limiting (100 requests/minute per IP)
- Suspicious pattern detection and blocking
- Session management with Redis backend
- Failed login attempt tracking with account lockout
- IP-based access restrictions per location

### Business Logic
- Double-entry bookkeeping implementation
- Multi-currency transaction processing
- Commission calculation framework
- Exchange rate management with historical tracking
- Customer balance tracking with credit limits
- Multi-step transaction support with progress tracking
- Delivery method handling (in-person, courier, bank transfer, SWIFT)
- WhatsApp Desktop integration framework

## [0.1.0] - 2025-08-11
- Docker-based dev stack scaffolded under deployment/ with docker-compose, Dockerfile, Nginx config, requirements; .env.example added

### Added
- Deployment Guide (docs/technical/deployment_guide.md) targeting Windows Server + Nginx + Gunicorn + Docker
- Configuration Guide (docs/technical/configuration_guide.md) covering ENV, security, roles, per-location rates
- Troubleshooting Guide (docs/technical/troubleshooting_guide.md)

### Changed
- Updated docs/technical/module_specifications.md to reflect Arena Doviz business rules:
  - Access limited to employees/managers; no direct access for customers/couriers
  - Roles adjusted: Admin, Accountant/Branch Employee, Viewer; Courier as operational role without login
  - WhatsApp Desktop notification flow (preview/approve/send)
  - Expanded transaction types and commission handling, multi-step payments, attachments
  - Exchange rates per location with endpoints
  - Balance traceability (drill-down) and alerts
  - Reporting columns and profit per transaction/daily profit
  - Added multilingual section (XML-based)
- Updated docs/business/business_requirements.md to include:
  - Per-location latest buy/sell display
  - WhatsApp Desktop flow note
  - Attachment requirement in multi-step transactions

### Notes
- No code changes yet; documentation aligned with Statement Of Account expectations and new business notes.

