"""
Customers app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class CustomersConfig(AppConfig):
    """Configuration for the customers app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.customers'
    verbose_name = 'Arena Doviz Customers'
    
    def ready(self):
        """
        Initialize the customers app when Django starts.
        """
        logger.info("Arena Doviz Customers app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Customers app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import customers app signals: {e}")
        
        # Initialize customer-related services
        self._initialize_customer_services()
    
    def _initialize_customer_services(self):
        """Initialize customer management services."""
        logger.debug("Initializing customer services...")
        
        try:
            # Setup customer categories and default settings
            self._setup_customer_categories()
            logger.info("Customer services initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize customer services: {e}")
    
    def _setup_customer_categories(self):
        """Setup default customer categories."""
        # This will be implemented when the database is ready
        logger.debug("Customer categories setup completed")
