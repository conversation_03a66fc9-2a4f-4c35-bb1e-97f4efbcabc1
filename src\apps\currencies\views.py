"""
API views for Arena Doviz Currencies app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count
from django.utils import timezone
from decimal import Decimal
from .models import Currency, ExchangeRate, ExchangeRateHistory
from .rate_management import rate_manager, auto_updater
from .serializers import (
    CurrencySerializer, CurrencyListSerializer, ExchangeRateSerializer,
    ExchangeRateListSerializer, ExchangeRateCreateSerializer,
    ExchangeRateHistorySerializer, CurrencyStatsSerializer,
    CurrentRateSerializer, ExchangeCalculationSerializer,
    ExchangeCalculationResultSerializer
)
from apps.core.utils import log_user_action, get_client_ip, safe_decimal_conversion
import logging

logger = logging.getLogger(__name__)


class CurrencyViewSet(viewsets.ModelViewSet):
    """ViewSet for Currency management."""
    
    queryset = Currency.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['sort_order', 'code']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return CurrencyListSerializer
        return CurrencySerializer
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = self.queryset
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # Filter by base currency
        is_base_currency = self.request.query_params.get('is_base_currency')
        if is_base_currency is not None:
            queryset = queryset.filter(is_base_currency=is_base_currency.lower() == 'true')
        
        return queryset
    
    def perform_create(self, serializer):
        """Create currency with audit logging."""
        currency = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='create',
            model_name='Currency',
            object_id=str(currency.id),
            object_repr=str(currency),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Currency created: {currency.code} by {self.request.user.username}")
    
    def perform_update(self, serializer):
        """Update currency with audit logging."""
        old_instance = self.get_object()
        currency = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='update',
            model_name='Currency',
            object_id=str(currency.id),
            object_repr=str(currency),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Currency updated: {currency.code} by {self.request.user.username}")
    
    def perform_destroy(self, instance):
        """Soft delete currency with audit logging."""
        instance.delete(user=self.request.user)
        
        log_user_action(
            user=self.request.user,
            action='delete',
            model_name='Currency',
            object_id=str(instance.id),
            object_repr=str(instance),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Currency deleted: {instance.code} by {self.request.user.username}")
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate currency."""
        currency = self.get_object()
        
        if not currency.is_active:
            currency.is_active = True
            currency.save(update_fields=['is_active'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Currency',
                object_id=str(currency.id),
                object_repr=str(currency),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'activated'}
            )
            
            return Response({'message': 'Currency activated successfully'})
        
        return Response({'message': 'Currency is already active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate currency."""
        currency = self.get_object()
        
        if currency.is_active:
            # Check if this is the base currency
            if currency.is_base_currency:
                return Response(
                    {'message': 'Cannot deactivate base currency'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            currency.is_active = False
            currency.save(update_fields=['is_active'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Currency',
                object_id=str(currency.id),
                object_repr=str(currency),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'deactivated'}
            )
            
            return Response({'message': 'Currency deactivated successfully'})
        
        return Response({'message': 'Currency is not active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def set_base_currency(self, request, pk=None):
        """Set currency as base currency."""
        currency = self.get_object()
        
        if not currency.is_base_currency:
            # Remove base currency status from other currencies
            Currency.objects.filter(is_base_currency=True).update(is_base_currency=False)
            
            # Set this currency as base currency
            currency.is_base_currency = True
            currency.save(update_fields=['is_base_currency'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Currency',
                object_id=str(currency.id),
                object_repr=str(currency),
                ip_address=get_client_ip(request),
                additional_data={'base_currency_changed': True}
            )
            
            return Response({'message': 'Currency set as base currency successfully'})
        
        return Response({'message': 'Currency is already the base currency'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get currency statistics."""
        # Calculate statistics
        total_currencies = Currency.objects.filter(is_deleted=False).count()
        active_currencies = Currency.objects.filter(is_active=True, is_deleted=False).count()
        
        # Base currency
        base_currency = Currency.get_base_currency()
        base_currency_code = base_currency.code if base_currency else 'Not set'
        
        # Exchange rates
        total_exchange_rates = ExchangeRate.objects.filter(is_deleted=False).count()
        active_exchange_rates = ExchangeRate.objects.filter(is_active=True, is_deleted=False).count()
        
        # Currencies by decimal places
        currencies_by_decimal_places = dict(
            Currency.objects.filter(is_deleted=False)
            .values('decimal_places')
            .annotate(count=Count('id'))
            .values_list('decimal_places', 'count')
        )
        
        # Latest rate updates (last 24 hours)
        yesterday = timezone.now() - timezone.timedelta(days=1)
        latest_rate_updates = ExchangeRate.objects.filter(
            updated_at__gte=yesterday,
            is_deleted=False
        ).count()
        
        stats_data = {
            'total_currencies': total_currencies,
            'active_currencies': active_currencies,
            'base_currency': base_currency_code,
            'total_exchange_rates': total_exchange_rates,
            'active_exchange_rates': active_exchange_rates,
            'currencies_by_decimal_places': currencies_by_decimal_places,
            'latest_rate_updates': latest_rate_updates
        }
        
        serializer = CurrencyStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active currencies."""
        active_currencies = Currency.get_active_currencies()
        serializer = CurrencyListSerializer(active_currencies, many=True)
        return Response(serializer.data)


class ExchangeRateViewSet(viewsets.ModelViewSet):
    """ViewSet for ExchangeRate management."""
    
    queryset = ExchangeRate.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-effective_from']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return ExchangeRateListSerializer
        elif self.action == 'create':
            return ExchangeRateCreateSerializer
        return ExchangeRateSerializer
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = self.queryset
        
        # Filter by currencies
        from_currency = self.request.query_params.get('from_currency')
        if from_currency:
            queryset = queryset.filter(from_currency__code=from_currency)
        
        to_currency = self.request.query_params.get('to_currency')
        if to_currency:
            queryset = queryset.filter(to_currency__code=to_currency)
        
        # Filter by location
        location = self.request.query_params.get('location')
        if location:
            queryset = queryset.filter(location__code=location)
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # Filter by current rates only
        current_only = self.request.query_params.get('current_only')
        if current_only and current_only.lower() == 'true':
            now = timezone.now()
            queryset = queryset.filter(
                effective_from__lte=now,
                is_active=True
            ).filter(
                models.Q(effective_until__isnull=True) | models.Q(effective_until__gt=now)
            )
        
        return queryset.select_related('from_currency', 'to_currency', 'location')
    
    def perform_create(self, serializer):
        """Create exchange rate with audit logging and history tracking."""
        # Check if there's an existing current rate for the same currency pair and location
        from_currency = serializer.validated_data['from_currency']
        to_currency = serializer.validated_data['to_currency']
        location = serializer.validated_data['location']
        
        existing_rate = ExchangeRate.objects.filter(
            from_currency=from_currency,
            to_currency=to_currency,
            location=location,
            is_active=True,
            is_deleted=False,
            effective_until__isnull=True
        ).first()
        
        # Create new rate
        exchange_rate = serializer.save()
        
        # If there was an existing rate, end it and create history
        if existing_rate:
            existing_rate.effective_until = exchange_rate.effective_from
            existing_rate.save(update_fields=['effective_until'])
            
            # Create history entry
            ExchangeRateHistory.objects.create(
                exchange_rate=exchange_rate,
                old_buy_rate=existing_rate.buy_rate,
                new_buy_rate=exchange_rate.buy_rate,
                old_sell_rate=existing_rate.sell_rate,
                new_sell_rate=exchange_rate.sell_rate,
                changed_by=self.request.user,
                reason=f"New rate created for {from_currency.code}/{to_currency.code}"
            )
        
        log_user_action(
            user=self.request.user,
            action='create',
            model_name='ExchangeRate',
            object_id=str(exchange_rate.id),
            object_repr=str(exchange_rate),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Exchange rate created: {exchange_rate} by {self.request.user.username}")
    
    def perform_update(self, serializer):
        """Update exchange rate with audit logging and history tracking."""
        old_instance = self.get_object()
        old_buy_rate = old_instance.buy_rate
        old_sell_rate = old_instance.sell_rate
        
        exchange_rate = serializer.save()
        
        # Create history entry if rates changed
        if old_buy_rate != exchange_rate.buy_rate or old_sell_rate != exchange_rate.sell_rate:
            ExchangeRateHistory.objects.create(
                exchange_rate=exchange_rate,
                old_buy_rate=old_buy_rate,
                new_buy_rate=exchange_rate.buy_rate,
                old_sell_rate=old_sell_rate,
                new_sell_rate=exchange_rate.sell_rate,
                changed_by=self.request.user,
                reason="Rate updated"
            )
        
        log_user_action(
            user=self.request.user,
            action='update',
            model_name='ExchangeRate',
            object_id=str(exchange_rate.id),
            object_repr=str(exchange_rate),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Exchange rate updated: {exchange_rate} by {self.request.user.username}")
    
    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current exchange rates."""
        location_code = request.query_params.get('location')
        
        if not location_code:
            return Response(
                {'error': 'Location parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from apps.locations.models import Location
            location = Location.objects.get(code=location_code, is_active=True, is_deleted=False)
        except Location.DoesNotExist:
            return Response(
                {'error': 'Location not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get latest rates for the location
        rates = ExchangeRate.get_latest_rates_by_location(location)
        
        # Format response
        current_rates = []
        for rate in rates:
            current_rates.append({
                'from_currency': rate.from_currency.code,
                'to_currency': rate.to_currency.code,
                'location': rate.location.code,
                'buy_rate': rate.buy_rate,
                'sell_rate': rate.sell_rate,
                'spread': rate.get_spread(),
                'spread_percentage': rate.get_spread_percentage(),
                'effective_from': rate.effective_from,
                'last_updated': rate.updated_at
            })
        
        serializer = CurrentRateSerializer(current_rates, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def calculate(self, request):
        """Calculate exchange amount."""
        serializer = ExchangeCalculationSerializer(data=request.data)
        
        if serializer.is_valid():
            from_currency = serializer.validated_data['from_currency']
            to_currency = serializer.validated_data['to_currency']
            location = serializer.validated_data['location']
            amount = serializer.validated_data['amount']
            rate_type = serializer.validated_data['rate_type']
            
            try:
                # Get current rate
                rate = ExchangeRate.get_current_rate(
                    from_currency, to_currency, location, rate_type
                )
                
                if not rate:
                    return Response(
                        {'error': f'No current {rate_type} rate found for {from_currency}/{to_currency} at {location}'},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                # Calculate exchange amount
                to_amount = amount * rate
                
                result = {
                    'from_currency': from_currency,
                    'to_currency': to_currency,
                    'location': location,
                    'from_amount': amount,
                    'to_amount': to_amount,
                    'exchange_rate': rate,
                    'rate_type': rate_type,
                    'calculation_time': timezone.now()
                }
                
                result_serializer = ExchangeCalculationResultSerializer(result)
                return Response(result_serializer.data)
                
            except Exception as e:
                logger.error(f"Error calculating exchange: {e}")
                return Response(
                    {'error': 'Failed to calculate exchange amount'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_with_approval(self, request):
        """Create exchange rate with approval workflow."""
        try:
            rate_data = {
                'from_currency': request.data.get('from_currency'),
                'to_currency': request.data.get('to_currency'),
                'location': request.data.get('location'),
                'buy_rate': Decimal(str(request.data.get('buy_rate', 0))),
                'sell_rate': Decimal(str(request.data.get('sell_rate', 0))),
                'effective_from': request.data.get('effective_from'),
                'source': request.data.get('source', 'manual'),
                'notes': request.data.get('notes', '')
            }

            # Validate required fields
            required_fields = ['from_currency', 'to_currency', 'location', 'buy_rate', 'sell_rate']
            for field in required_fields:
                if not rate_data.get(field):
                    return Response(
                        {'error': f'Field {field} is required'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # Get currency and location objects
            from apps.currencies.models import Currency
            from apps.locations.models import Location

            try:
                rate_data['from_currency'] = Currency.objects.get(id=rate_data['from_currency'])
                rate_data['to_currency'] = Currency.objects.get(id=rate_data['to_currency'])
                rate_data['location'] = Location.objects.get(id=rate_data['location'])
            except (Currency.DoesNotExist, Location.DoesNotExist) as e:
                return Response(
                    {'error': f'Invalid reference: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create rate with approval workflow
            rate, approval_required = rate_manager.create_rate_with_approval(
                rate_data, request.user
            )

            serializer = ExchangeRateSerializer(rate)

            return Response({
                'rate': serializer.data,
                'approval_required': approval_required,
                'message': 'Rate created and pending approval' if approval_required else 'Rate created and activated'
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating rate with approval: {str(e)}")
            return Response(
                {'error': 'Failed to create exchange rate'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a pending exchange rate."""
        if not request.user.can_approve_transactions():
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            notes = request.data.get('notes', '')
            rate = rate_manager.approve_rate(pk, request.user, notes)

            serializer = ExchangeRateSerializer(rate)
            return Response({
                'rate': serializer.data,
                'message': 'Rate approved successfully'
            })

        except Exception as e:
            logger.error(f"Error approving rate {pk}: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a pending exchange rate."""
        if not request.user.can_approve_transactions():
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            reason = request.data.get('reason', 'No reason provided')
            rate_manager.reject_rate(pk, request.user, reason)

            return Response({
                'message': 'Rate rejected successfully'
            })

        except Exception as e:
            logger.error(f"Error rejecting rate {pk}: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def validate_rate(self, request):
        """Validate exchange rate changes."""
        try:
            rate_data = {
                'from_currency': Currency.objects.get(id=request.data.get('from_currency')),
                'to_currency': Currency.objects.get(id=request.data.get('to_currency')),
                'location': Location.objects.get(id=request.data.get('location')),
                'buy_rate': Decimal(str(request.data.get('buy_rate', 0))),
                'sell_rate': Decimal(str(request.data.get('sell_rate', 0)))
            }

            validation_result = rate_manager.validate_rate_change(rate_data)

            return Response(validation_result)

        except Exception as e:
            logger.error(f"Error validating rate: {str(e)}")
            return Response(
                {'error': 'Failed to validate rate'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def recommendations(self, request):
        """Get rate recommendations based on historical data."""
        try:
            from_currency_id = request.query_params.get('from_currency')
            to_currency_id = request.query_params.get('to_currency')
            location_id = request.query_params.get('location')

            if not all([from_currency_id, to_currency_id, location_id]):
                return Response(
                    {'error': 'from_currency, to_currency, and location parameters are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            from_currency = Currency.objects.get(id=from_currency_id)
            to_currency = Currency.objects.get(id=to_currency_id)
            location = Location.objects.get(id=location_id)

            recommendations = rate_manager.get_rate_recommendations(
                from_currency, to_currency, location
            )

            return Response(recommendations)

        except Exception as e:
            logger.error(f"Error getting recommendations: {str(e)}")
            return Response(
                {'error': 'Failed to get recommendations'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def pending_approval(self, request):
        """Get rates pending approval."""
        if not request.user.can_approve_transactions():
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        pending_rates = self.get_queryset().filter(
            is_active=False,
            approved_by__isnull=True
        )

        serializer = ExchangeRateListSerializer(pending_rates, many=True)
        return Response(serializer.data)


class ExchangeRateHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for ExchangeRateHistory (read-only)."""
    
    queryset = ExchangeRateHistory.objects.all()
    serializer_class = ExchangeRateHistorySerializer
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['-created_at']
    
    def get_queryset(self):
        """Filter history based on query parameters."""
        queryset = self.queryset
        
        # Filter by exchange rate
        exchange_rate_id = self.request.query_params.get('exchange_rate')
        if exchange_rate_id:
            queryset = queryset.filter(exchange_rate_id=exchange_rate_id)
        
        # Filter by user
        changed_by = self.request.query_params.get('changed_by')
        if changed_by:
            queryset = queryset.filter(changed_by_id=changed_by)
        
        # Date range filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        return queryset.select_related('exchange_rate', 'changed_by')
