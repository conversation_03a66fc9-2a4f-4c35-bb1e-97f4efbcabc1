"""
Transactions app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class TransactionsConfig(AppConfig):
    """Configuration for the transactions app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.transactions'
    verbose_name = 'Arena Doviz Transactions'
    
    def ready(self):
        """
        Initialize the transactions app when Django starts.
        """
        logger.info("Arena Doviz Transactions app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Transactions app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import transactions app signals: {e}")
        
        # Initialize transaction services
        self._initialize_transaction_services()
    
    def _initialize_transaction_services(self):
        """Initialize transaction processing services."""
        logger.debug("Initializing transaction services...")
        
        try:
            # Setup transaction types and default settings
            self._setup_transaction_types()
            logger.info("Transaction services initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize transaction services: {e}")
    
    def _setup_transaction_types(self):
        """Setup default transaction types."""
        # This will be implemented when the database is ready
        logger.debug("Transaction types setup completed")
