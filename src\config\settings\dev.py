"""
Development settings for Arena Doviz Exchange Accounting System.
"""

from .base import *
import logging

# Debug settings
DEBUG = True
ALLOWED_HOSTS = ['*']

# Database for development
DATABASES['default'].update({
    'HOST': 'localhost',
    'PORT': '5432',
})

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static files for development
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Development-specific logging
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['root']['level'] = 'DEBUG'

# Disable HTTPS redirects in development
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Django Debug Toolbar (if installed)
try:
    import debug_toolbar
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE = ['debug_toolbar.middleware.DebugToolbarMiddleware'] + MIDD<PERSON>WARE
    INTERNAL_IPS = ['127.0.0.1', 'localhost']
except ImportError:
    pass

# Development-specific Arena Doviz settings
ARENA_DOVIZ.update({
    'DEBUG_MODE': True,
    'MOCK_WHATSAPP': True,  # Mock WhatsApp integration in development
    'ENABLE_TEST_DATA': True,  # Allow test data creation
})

# Logging configuration for development
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
