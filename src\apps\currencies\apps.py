"""
Currencies app configuration for Arena Doviz Exchange Accounting System.
"""

from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class CurrenciesConfig(AppConfig):
    """Configuration for the currencies app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.currencies'
    verbose_name = 'Arena Doviz Currencies'
    
    def ready(self):
        """
        Initialize the currencies app when Django starts.
        """
        logger.info("Arena Doviz Currencies app is ready")
        
        # Import signal handlers
        try:
            from . import signals
            logger.debug("Currencies app signals imported successfully")
        except ImportError as e:
            logger.warning(f"Could not import currencies app signals: {e}")
        
        # Initialize default currencies
        self._initialize_default_currencies()
    
    def _initialize_default_currencies(self):
        """Initialize default currencies based on business requirements."""
        logger.debug("Initializing default currencies...")
        
        try:
            # Check if the currencies table exists
            from django.db import connection
            from django.db.utils import OperationalError
            
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'currencies_currency'
                    );
                """)
                table_exists = cursor.fetchone()[0]
            
            if table_exists:
                self._create_default_currencies()
            else:
                logger.debug("Currencies table does not exist yet, skipping default currency creation")
                
        except (OperationalError, Exception) as e:
            logger.debug(f"Could not initialize default currencies: {e}")
    
    def _create_default_currencies(self):
        """Create default currencies if they don't exist."""
        from .models import Currency
        
        # Default currencies based on business requirements
        default_currencies = [
            {
                'code': 'USD',
                'name': 'US Dollar',
                'symbol': '$',
                'decimal_places': 2,
                'is_active': True,
                'is_base_currency': True,  # USD as base currency
                'sort_order': 1,
            },
            {
                'code': 'AED',
                'name': 'UAE Dirham',
                'symbol': 'د.إ',
                'decimal_places': 2,
                'is_active': True,
                'is_base_currency': False,
                'sort_order': 2,
            },
            {
                'code': 'IRR',
                'name': 'Iranian Rial',
                'symbol': '﷼',
                'decimal_places': 0,  # IRR typically doesn't use decimal places
                'is_active': True,
                'is_base_currency': False,
                'sort_order': 3,
            },
        ]
        
        for currency_data in default_currencies:
            currency, created = Currency.objects.get_or_create(
                code=currency_data['code'],
                defaults=currency_data
            )
            if created:
                logger.info(f"Created default currency: {currency.code} - {currency.name}")
            else:
                logger.debug(f"Default currency already exists: {currency.code}")
