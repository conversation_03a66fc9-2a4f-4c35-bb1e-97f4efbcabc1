"""
API URL configuration for Arena Doviz Currencies app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import CurrencyViewSet, ExchangeRateViewSet, ExchangeRateHistoryViewSet
from django.views.generic import TemplateView

app_name = 'currencies'

# API router for currencies endpoints
router = DefaultRouter()
router.register(r'currencies', CurrencyViewSet)
router.register(r'rates', ExchangeRateViewSet)
router.register(r'rate-history', ExchangeRateHistoryViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('manage/', TemplateView.as_view(template_name='currencies/rate_management.html'), name='rate_management'),
]
