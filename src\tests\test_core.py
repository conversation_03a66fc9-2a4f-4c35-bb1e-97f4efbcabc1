"""
Tests for Arena Doviz Core app.
Tests dashboard functionality, analytics, and core utilities.
"""

from django.test import TestCase
from rest_framework import status
from decimal import Decimal
from datetime import timedelta
from django.utils import timezone
from .base import BaseTestCase, BaseAPITestCase, TestDataMixin
from apps.core.views import DashboardViewSet
from apps.transactions.models import Transaction


class DashboardAPITest(BaseAPITestCase, TestDataMixin):
    """Test Dashboard API endpoints."""
    
    def setUp(self):
        """Set up test data for dashboard tests."""
        super().setUp()
        
        # Create test transactions for analytics
        self.create_transaction(
            from_amount=Decimal('1000.00'),
            commission_amount=Decimal('15.00'),
            status=Transaction.Status.COMPLETED,
            created_at=timezone.now() - timedelta(days=1)
        )
        
        self.create_transaction(
            from_amount=Decimal('2000.00'),
            commission_amount=Decimal('30.00'),
            status=Transaction.Status.COMPLETED,
            created_at=timezone.now() - timedelta(days=2)
        )
        
        self.create_transaction(
            from_amount=Decimal('500.00'),
            commission_amount=Decimal('7.50'),
            status=Transaction.Status.PENDING,
            created_at=timezone.now() - timedelta(days=3)
        )
    
    def test_chart_data_endpoint(self):
        """Test chart data API endpoint."""
        url = '/api/v1/core/dashboard/chart_data/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check response structure
        self.assertIn('transaction_volume', response.data)
        self.assertIn('currency_distribution', response.data)
        self.assertIn('profit_analysis', response.data)
        self.assertIn('balance_trends', response.data)
        self.assertIn('status_distribution', response.data)
        self.assertIn('date_range', response.data)
        
        # Check transaction volume data structure
        volume_data = response.data['transaction_volume']
        self.assertIn('labels', volume_data)
        self.assertIn('datasets', volume_data)
        self.assertIsInstance(volume_data['labels'], list)
        self.assertIsInstance(volume_data['datasets'], list)
        
        # Check currency distribution data structure
        currency_data = response.data['currency_distribution']
        self.assertIn('labels', currency_data)
        self.assertIn('datasets', currency_data)
        
        # Check profit analysis data structure
        profit_data = response.data['profit_analysis']
        self.assertIn('labels', profit_data)
        self.assertIn('datasets', profit_data)
        self.assertIn('total_commission', profit_data)
    
    def test_chart_data_with_date_filter(self):
        """Test chart data with date range filter."""
        url = '/api/v1/core/dashboard/chart_data/?days=7'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check that date range is reflected in response
        date_range = response.data['date_range']
        self.assertEqual(date_range['days'], 7)
    
    def test_chart_data_location_filtering(self):
        """Test chart data filtering by location for non-admin users."""
        # Test with employee user (should only see their location's data)
        self.authenticate_as(self.employee_user)
        
        url = '/api/v1/core/dashboard/chart_data/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Data should be filtered by user's location
        # (Specific assertions would depend on the actual data)
        self.assertIsNotNone(response.data['transaction_volume'])
    
    def test_chart_data_admin_access(self):
        """Test that admin users get location performance data."""
        url = '/api/v1/core/dashboard/chart_data/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Admin should get location performance data
        self.assertIn('location_performance', response.data)
        self.assertIsNotNone(response.data['location_performance'])
    
    def test_chart_data_non_admin_access(self):
        """Test that non-admin users don't get location performance data."""
        self.authenticate_as(self.employee_user)
        
        url = '/api/v1/core/dashboard/chart_data/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Non-admin should not get location performance data
        self.assertIsNone(response.data['location_performance'])
    
    def test_export_data_endpoint(self):
        """Test data export endpoint."""
        url = '/api/v1/core/dashboard/export_data/'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Check that response is CSV
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
        
        # Check CSV content
        content = response.content.decode('utf-8')
        self.assertIn('Transaction Number', content)
        self.assertIn('Date', content)
        self.assertIn('Customer', content)
        self.assertIn('Amount', content)
    
    def test_export_data_with_filters(self):
        """Test data export with filters."""
        url = '/api/v1/core/dashboard/export_data/?days=7'
        response = self.client.get(url)
        self.assertAPISuccess(response)
        
        # Should still be CSV format
        self.assertEqual(response['Content-Type'], 'text/csv')
    
    def test_dashboard_authentication_required(self):
        """Test that dashboard endpoints require authentication."""
        self.unauthenticate()
        
        url = '/api/v1/core/dashboard/chart_data/'
        response = self.client.get(url)
        self.assertAPIPermissionDenied(response)


class CoreUtilitiesTest(BaseTestCase):
    """Test core utility functions."""
    
    def test_format_currency(self):
        """Test currency formatting utility."""
        from apps.core.utils import format_currency
        
        # Test USD formatting
        formatted = format_currency(Decimal('1234.56'), self.usd)
        self.assertEqual(formatted, '$1,234.56')
        
        # Test AED formatting
        formatted = format_currency(Decimal('1000.00'), self.aed)
        self.assertEqual(formatted, 'د.إ1,000.00')
        
        # Test IRR formatting (no decimal places)
        formatted = format_currency(Decimal('1000000'), self.irr)
        self.assertEqual(formatted, '﷼1,000,000')
    
    def test_calculate_exchange_amount(self):
        """Test exchange amount calculation utility."""
        from apps.core.utils import calculate_exchange_amount
        
        amount = Decimal('1000.00')
        rate = Decimal('3.67')
        
        result = calculate_exchange_amount(amount, rate)
        expected = amount * rate
        self.assertDecimalEqual(result, expected)
    
    def test_validate_exchange_rate(self):
        """Test exchange rate validation utility."""
        from apps.core.utils import validate_exchange_rate
        
        # Valid rate
        self.assertTrue(validate_exchange_rate(Decimal('3.67')))
        
        # Invalid rates
        self.assertFalse(validate_exchange_rate(Decimal('0')))
        self.assertFalse(validate_exchange_rate(Decimal('-1.5')))
    
    def test_generate_reference_number(self):
        """Test reference number generation utility."""
        from apps.core.utils import generate_reference_number
        
        ref1 = generate_reference_number('TXN')
        ref2 = generate_reference_number('TXN')
        
        # Should be different
        self.assertNotEqual(ref1, ref2)
        
        # Should start with prefix
        self.assertTrue(ref1.startswith('TXN'))
        self.assertTrue(ref2.startswith('TXN'))
        
        # Should have timestamp component
        self.assertGreater(len(ref1), 3)  # More than just the prefix


class BaseModelTest(BaseTestCase):
    """Test base model functionality."""
    
    def test_base_model_fields(self):
        """Test that base model fields are present."""
        from apps.core.models import BaseModel
        
        # Test with a model that inherits from BaseModel
        user = self.admin_user
        
        # Should have base fields
        self.assertIsNotNone(user.id)
        self.assertIsNotNone(user.created_at)
        self.assertIsNotNone(user.updated_at)
        self.assertFalse(user.is_deleted)
    
    def test_soft_delete(self):
        """Test soft delete functionality."""
        user = self.create_test_user('deletetest')
        user_id = user.id
        
        # Soft delete
        user.delete()
        
        # Should be marked as deleted but still in database
        user.refresh_from_db()
        self.assertTrue(user.is_deleted)
        
        # Should not appear in default queryset
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        with self.assertRaises(User.DoesNotExist):
            User.objects.get(id=user_id)
        
        # Should appear in all_objects queryset
        deleted_user = User.all_objects.get(id=user_id)
        self.assertTrue(deleted_user.is_deleted)


class PermissionTest(BaseTestCase):
    """Test permission system."""
    
    def test_role_based_permissions(self):
        """Test that role-based permissions work correctly."""
        # Admin permissions
        self.assertTrue(self.admin_user.can_manage_users())
        self.assertTrue(self.admin_user.can_approve_transactions())
        self.assertTrue(self.admin_user.can_complete_transactions())
        self.assertTrue(self.admin_user.can_view_all_locations())
        
        # Accountant permissions
        self.assertFalse(self.accountant_user.can_manage_users())
        self.assertTrue(self.accountant_user.can_approve_transactions())
        self.assertTrue(self.accountant_user.can_complete_transactions())
        self.assertFalse(self.accountant_user.can_view_all_locations())
        
        # Employee permissions
        self.assertFalse(self.employee_user.can_manage_users())
        self.assertFalse(self.employee_user.can_approve_transactions())
        self.assertTrue(self.employee_user.can_complete_transactions())
        self.assertFalse(self.employee_user.can_view_all_locations())
        
        # Viewer permissions
        self.assertFalse(self.viewer_user.can_manage_users())
        self.assertFalse(self.viewer_user.can_approve_transactions())
        self.assertFalse(self.viewer_user.can_complete_transactions())
        self.assertFalse(self.viewer_user.can_view_all_locations())
    
    def test_location_based_access(self):
        """Test location-based access control."""
        # Create user in different location
        other_location = self.create_test_location('OTHER', 'Other Location')
        other_user = self.create_test_user('otheruser', location=other_location)
        
        # Users should only access their own location's data by default
        self.assertEqual(self.employee_user.location, self.location)
        self.assertEqual(other_user.location, other_location)
        self.assertNotEqual(self.employee_user.location, other_user.location)
    
    def create_test_location(self, code, name):
        """Helper method to create test location."""
        from apps.locations.models import Location
        return Location.objects.create(
            code=code,
            name=name,
            address='Test Address',
            is_active=True
        )


class ValidationTest(BaseTestCase):
    """Test validation utilities."""
    
    def test_phone_number_validation(self):
        """Test phone number validation."""
        from apps.core.validators import validate_phone_number
        
        # Valid phone numbers
        self.assertTrue(validate_phone_number('+1234567890'))
        self.assertTrue(validate_phone_number('+971501234567'))
        
        # Invalid phone numbers
        self.assertFalse(validate_phone_number('1234567890'))  # No country code
        self.assertFalse(validate_phone_number('+123'))  # Too short
        self.assertFalse(validate_phone_number('abc123'))  # Contains letters
    
    def test_email_validation(self):
        """Test email validation."""
        from django.core.validators import validate_email
        from django.core.exceptions import ValidationError
        
        # Valid emails
        try:
            validate_email('<EMAIL>')
            validate_email('<EMAIL>')
        except ValidationError:
            self.fail("Valid email should not raise ValidationError")
        
        # Invalid emails
        with self.assertRaises(ValidationError):
            validate_email('invalid-email')
        
        with self.assertRaises(ValidationError):
            validate_email('@domain.com')
    
    def test_amount_validation(self):
        """Test amount validation."""
        from apps.core.validators import validate_positive_amount
        
        # Valid amounts
        self.assertTrue(validate_positive_amount(Decimal('100.00')))
        self.assertTrue(validate_positive_amount(Decimal('0.01')))
        
        # Invalid amounts
        self.assertFalse(validate_positive_amount(Decimal('0')))
        self.assertFalse(validate_positive_amount(Decimal('-100.00')))


class SecurityTest(BaseTestCase):
    """Test security features."""
    
    def test_password_hashing(self):
        """Test that passwords are properly hashed."""
        user = self.create_test_user('securitytest', password='testpass123')
        
        # Password should be hashed, not stored in plain text
        self.assertNotEqual(user.password, 'testpass123')
        self.assertTrue(user.check_password('testpass123'))
        self.assertFalse(user.check_password('wrongpass'))
    
    def test_sensitive_data_logging(self):
        """Test that sensitive data is not logged."""
        from apps.core.utils import sanitize_log_data
        
        sensitive_data = {
            'password': 'secret123',
            'credit_card': '1234567890123456',
            'ssn': '***********',
            'safe_field': 'this is ok'
        }
        
        sanitized = sanitize_log_data(sensitive_data)
        
        self.assertEqual(sanitized['password'], '[REDACTED]')
        self.assertEqual(sanitized['credit_card'], '[REDACTED]')
        self.assertEqual(sanitized['ssn'], '[REDACTED]')
        self.assertEqual(sanitized['safe_field'], 'this is ok')
    
    def test_sql_injection_protection(self):
        """Test protection against SQL injection."""
        # This test ensures that Django's ORM protects against SQL injection
        # by using parameterized queries
        
        malicious_input = "'; DROP TABLE auth_user; --"
        
        # This should not cause any issues due to Django's ORM protection
        users = self.admin_user.__class__.objects.filter(username=malicious_input)
        self.assertEqual(users.count(), 0)
        
        # Verify that the users table still exists and has data
        total_users = self.admin_user.__class__.objects.count()
        self.assertGreater(total_users, 0)
