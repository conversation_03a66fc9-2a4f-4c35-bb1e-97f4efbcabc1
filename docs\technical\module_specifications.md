# 🔧 مشخصات ماژول‌های سیستم حسابداری صرافی

## 📋 نمای کلی ماژول‌ها

سیستم حسابداری صرافی شامل ماژول‌های زیر است:

## 🔐 ماژول احراز هویت و مجوزها (Authentication & Authorization)

### مسئولیت‌ها
- مدیریت ورود و خروج کاربران
- کنترل دسترسی بر اساس نقش
- مدیریت نشست‌ها


### API Endpoints
```python
POST /api/auth/login/          # ورود کاربر
POST /api/auth/logout/         # خروج کاربر
POST /api/auth/refresh/        # تجدید توکن
GET  /api/auth/profile/        # پروفایل کاربر
PUT  /api/auth/profile/        # ویرایش پروفایل
POST /api/auth/change-password/ # تغییر رمز عبور
```

### مدل‌های داده
- User
- UserRole
- UserRoleAssignment
- UserSession

### محدودیت دسترسی
- دسترسی صرفاً برای کارکنان و مدیران است؛ مشتریان و پیک‌ها دسترسی مستقیم به سیستم ندارند.

## 👥 ماژول مدیریت کاربران (User Management)

### مسئولیت‌ها
- ایجاد و ویرایش کاربران
- تخصیص نقش‌ها
- مدیریت مجوزها
- غیرفعال‌سازی کاربران

### API Endpoints
```python
GET    /api/users/             # لیست کاربران
POST   /api/users/             # ایجاد کاربر جدید
GET    /api/users/{id}/        # جزئیات کاربر
PUT    /api/users/{id}/        # ویرایش کاربر
DELETE /api/users/{id}/        # حذف کاربر
POST   /api/users/{id}/roles/  # تخصیص نقش
```

### نقش‌های سیستم
- **Admin**: دسترسی کامل و تعریف مجوزها برای هر نقش/کاربر
- **Accountant / Branch Employee**: انجام عملیات حسابداری و ثبت معاملات در شعبه
- **Viewer**: مشاهده فقط
- **Courier (Delivery)**: نقش عملیاتی برای ثبت تحویل توسط کارکنان (بدون دسترسی ورود)

## 🏢 ماژول مدیریت مکان‌ها (Location Management)

### مسئولیت‌ها
- مدیریت دفاتر مختلف
- تخصیص مدیر به هر مکان
- مدیریت موجودی هر مکان
- تنظیمات خاص هر مکان

### API Endpoints
```python
GET    /api/locations/         # لیست مکان‌ها
POST   /api/locations/         # ایجاد مکان جدید
GET    /api/locations/{id}/    # جزئیات مکان
PUT    /api/locations/{id}/    # ویرایش مکان
GET    /api/locations/{id}/balances/ # موجودی مکان
```

### ویژگی‌ها
- کد یکتا برای هر مکان
- آدرس و اطلاعات تماس
- مدیر مسئول
- وضعیت فعال/غیرفعال

## 👤 ماژول مدیریت مشتریان (Customer Management)

### مسئولیت‌ها
- ثبت اطلاعات مشتریان
- مدیریت گروه‌های واتساپ
- پیگیری تاریخچه معاملات
- مدیریت موجودی مشتریان

### API Endpoints
```python
GET    /api/customers/         # لیست مشتریان
POST   /api/customers/         # ایجاد مشتری جدید
GET    /api/customers/{id}/    # جزئیات مشتری
PUT    /api/customers/{id}/    # ویرایش مشتری
GET    /api/customers/{id}/balances/    # موجودی مشتری
GET    /api/customers/{id}/transactions/ # معاملات مشتری
```

### فیلدهای اطلاعات
- نام و نام خانوادگی
- شماره تلفن
- نام شرکت یا یادداشت تکمیلی
- ایمیل (اختیاری)
- یادداشت‌ها
- شناسه گروه واتساپ (پس از ثبت، گروه اختصاصی ایجاد می‌شود)

## 💱 ماژول مدیریت ارزها (Currency Management)

### مسئولیت‌ها
- تعریف ارزهای مختلف
- مدیریت نرخ ارز
- محاسبه تبدیل ارز
- تاریخچه نرخ‌ها

### API Endpoints
```python
GET    /api/currencies/                    # لیست ارزها
POST   /api/currencies/                    # ایجاد ارز جدید
GET    /api/currencies/{id}/               # جزئیات ارز
PUT    /api/currencies/{id}/               # ویرایش ارز
GET    /api/exchange-rates/                # نرخ‌های ارز
POST   /api/exchange-rates/                # ثبت نرخ جدید
GET    /api/exchange-rates/latest/         # آخرین نرخ‌ها (به تفکیک مکان)
GET    /api/exchange-rates/{location}/     # نرخ‌های یک مکان مشخص
```

### ارزهای پیش‌فرض
- دلار آمریکا (USD)
- درهم امارات (AED)
- تومان ایران (IRR)

## 💰 ماژول مدیریت معاملات (Transaction Management)

### مسئولیت‌ها
- ثبت انواع معاملات (خرید/فروش ارز، انتقال داخلی بین حساب‌های مشتریان، پرداخت/دریافت ریالی نقدی/بانکی، حواله SWIFT)
- محاسبه کمیسیون (درصدی یا مبلغ ثابت مثل 3$ به ازای هر 1000$)
- مدیریت وضعیت معاملات و اقساط چندمرحله‌ای
- پیوست اسناد (رسید/اسکرین‌شات/فیش بانکی)

### API Endpoints
```python
GET    /api/transactions/      # لیست معاملات
POST   /api/transactions/      # ایجاد معامله جدید
GET    /api/transactions/{id}/ # جزئیات معامله
PUT    /api/transactions/{id}/ # ویرایش معامله
POST   /api/transactions/{id}/approve/ # تأیید معامله
POST   /api/transactions/{id}/cancel/  # لغو معامله
POST   /api/transactions/{id}/attachments/ # پیوست فایل
```

### انواع معاملات
- خرید ارز از مشتری (Customer → Exchange Office)
- فروش ارز به مشتری (Exchange Office → Customer)
- انتقال داخلی بین حساب‌های مشتریان
- تحویل فیزیکی (حضوری/پیک) و ثبت تحویل‌ها
- انتقال بانکی (ریالی یا ارزی)
- حواله SWIFT
- پرداخت/دریافت تومان (نقد/بانکی)

## 📊 ماژول موجودی (Balance Management)

### مسئولیت‌ها
- محاسبه موجودی لحظه‌ای
- به‌روزرسانی خودکار موجودی
- کنترل موجودی منفی
- گزارش موجودی

### API Endpoints
```python
GET    /api/balances/customers/     # موجودی مشتریان
GET    /api/balances/company/       # موجودی شرکت
GET    /api/balances/summary/       # خلاصه موجودی
POST   /api/balances/adjust/        # تعدیل موجودی
```

### ویژگی‌ها
- موجودی به تفکیک ارز
- موجودی به تفکیک مکان
- تاریخچه تغییرات موجودی
- قابلیت ردیابی لیست معاملات سازنده موجودی فعلی (drill-down)
- هشدار موجودی کم/منفی

## 📈 ماژول گزارش‌گیری (Reporting)

### مسئولیت‌ها
- تولید صورت‌حساب مشتریان
- گزارش‌های مالی
- گزارش‌های عملیاتی
- خروجی PDF و Excel

### API Endpoints
```python
GET    /api/reports/statements/            # صورت‌حساب مشتری با ستون‌ها: تاریخ، کد داخلی، شماره تراکنش بانکی، مبلغ، کمیسیون، نوع عملیات
GET    /api/reports/transactions/          # گزارش معاملات
GET    /api/reports/balances/              # گزارش موجودی
GET    /api/reports/profits/               # گزارش سود (سود هر معامله و سود روزانه)
POST   /api/reports/generate/              # تولید گزارش و خروجی PDF/Excel
```

### انواع گزارش
- صورت‌حساب مشتری (پشتیبانی از تراکنش‌های چندمرحله‌ای، تراکنش‌های مرتبط بین مشتریان، ردیف خلاصه ورودی/خروجی هر ارز)
- گزارش معاملات روزانه
- گزارش موجودی
- گزارش سودآوری (سود هر معامله و تجمیع روزانه)
- گزارش کمیسیون

## 🚚 ماژول مدیریت پیک (Courier Management)

### مسئولیت‌ها
- ثبت اطلاعات پیک‌ها و ذخیره پروفایل برای استفاده مجدد در معاملات بعدی
- تخصیص پیک به معاملات
- ثبت هر تحویل (مشتری ↔ شعبه / پیک ↔ شعبه)
- پیگیری تحویل و مدیریت رسیدها (امضای مشتری/پیک، آپلود عکس رسید)

### API Endpoints
```python
GET    /api/couriers/          # لیست پیک‌ها
POST   /api/couriers/          # ایجاد پیک جدید
GET    /api/couriers/{id}/     # جزئیات پیک
PUT    /api/couriers/{id}/     # ویرایش پیک
GET    /api/couriers/{id}/deliveries/ # تحویل‌های پیک
```

## 📱 ماژول اعلان‌ها (Notification)

### مسئولیت‌ها
- ارسال اعلان‌های سیستمی
- یکپارچگی با واتساپ (باز شدن WhatsApp Desktop، پیش‌نمایش پیام، تأیید و ارسال توسط کاربر)
- اعلان تغییر نرخ
- اعلان معاملات مهم

### API Endpoints
```python
GET    /api/notifications/     # لیست اعلان‌ها
POST   /api/notifications/     # ارسال اعلان
PUT    /api/notifications/{id}/read/ # خوانده شده
GET    /api/notifications/settings/ # تنظیمات اعلان
```

## ⚙️ ماژول تنظیمات (Settings)

### مسئولیت‌ها
- تنظیمات عمومی سیستم
- تنظیمات کمیسیون
- تنظیمات امنیتی
- تنظیمات پشتیبان‌گیری

### API Endpoints
```python
GET    /api/settings/          # تنظیمات سیستم
PUT    /api/settings/          # ویرایش تنظیمات
GET    /api/settings/commission/ # تنظیمات کمیسیون
PUT    /api/settings/commission/ # ویرایش کمیسیون
```

## 🔍 ماژول حسابرسی (Audit)

### مسئولیت‌ها
- ثبت تمام عملیات
- ردیابی تغییرات
- گزارش‌های حسابرسی
- بازیابی اطلاعات

### API Endpoints
```python
GET    /api/audit/logs/        # لاگ‌های حسابرسی
GET    /api/audit/changes/     # تاریخچه تغییرات
GET    /api/audit/users/       # فعالیت کاربران
```

## 🔄 تعامل بین ماژول‌ها

### جریان داده اصلی
```
Authentication → User Management → Transaction → Balance → Reporting
```

### وابستگی‌ها
- همه ماژول‌ها به Authentication وابسته‌اند
- Transaction به Customer، Currency، Location وابسته است
- Balance به Transaction وابسته است
- Reporting به همه ماژول‌ها دسترسی دارد

## 📦 ساختار پکیج‌ها

```
src/
├── apps/
│   ├── authentication/
│   ├── users/
│   ├── customers/
│   ├── locations/
│   ├── currencies/
│   ├── transactions/
│   ├── balances/
│   ├── reports/
│   ├── couriers/
│   ├── notifications/
│   ├── settings/
│   └── audit/
├── core/
│   ├── models/
│   ├── serializers/
│   ├── views/
│   └── utils/

## 🌐 چندزبانه

- رابط کاربری دو زبانه (قابل توسعه)
- مکانیزم ترجمه مبتنی بر فایل‌های XML
- امکان افزودن زبان جدید بدون نیاز به ریستارت سیستم

└── config/
    ├── settings/
    ├── urls.py
    └── wsgi.py
```
