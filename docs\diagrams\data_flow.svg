<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="processGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c0392b;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="externalGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#27ae60;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#229954;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="1400" height="1000" fill="#f8f9fa"/>

  <!-- Title -->
  <rect x="0" y="0" width="1400" height="50" fill="#2c3e50"/>
  <text x="700" y="30" text-anchor="middle" fill="white" font-size="20" font-weight="bold" font-family="Arial">
    Arena Doviz - Data Flow Diagram
  </text>

  <!-- External Entities -->
  <rect x="50" y="100" width="150" height="80" fill="url(#externalGrad)" stroke="#229954" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="125" y="130" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">Company Staff</text>
  <text x="125" y="145" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Admin, Accountant</text>
  <text x="125" y="160" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Operator, Viewer</text>

  <rect x="1200" y="100" width="150" height="80" fill="url(#externalGrad)" stroke="#229954" stroke-width="2" rx="10" filter="url(#shadow)"/>
  <text x="1275" y="130" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">External Entities</text>
  <text x="1275" y="145" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Customers (no login)</text>
  <text x="1275" y="160" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Couriers (no login)</text>

  <!-- Process 1: Authentication -->
  <circle cx="350" cy="250" r="60" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
  <text x="350" y="240" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">1.0</text>
  <text x="350" y="255" text-anchor="middle" fill="white" font-size="10" font-family="Arial">User</text>
  <text x="350" y="270" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Authentication</text>

  <!-- Process 2: Transaction Management -->
  <circle cx="700" cy="300" r="70" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
  <text x="700" y="285" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">2.0</text>
  <text x="700" y="300" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Transaction</text>
  <text x="700" y="315" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Processing</text>

  <!-- Process 3: Balance Calculation -->
  <circle cx="1050" cy="350" r="65" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
  <text x="1050" y="335" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">3.0</text>
  <text x="1050" y="350" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Balance</text>
  <text x="1050" y="365" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Calculation</text>

  <!-- Process 4: Reporting -->
  <circle cx="350" cy="500" r="60" fill="url(#processGrad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
  <text x="350" y="490" text-anchor="middle" fill="white" font-size="11" font-weight="bold" font-family="Arial">4.0</text>
  <text x="350" y="505" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Report</text>
  <text x="350" y="520" text-anchor="middle" fill="white" font-size="10" font-family="Arial">Generation</text>

  <!-- Data Stores -->
  <rect x="300" y="750" width="200" height="40" fill="url(#dataGrad)" stroke="#c0392b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="400" y="775" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">D1: Users and Roles</text>

  <rect x="550" y="750" width="200" height="40" fill="url(#dataGrad)" stroke="#c0392b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="650" y="775" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">D2: Transactions</text>

  <rect x="800" y="750" width="200" height="40" fill="url(#dataGrad)" stroke="#c0392b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <!-- Rates by location note -->
  <text x="700" y="200" text-anchor="middle" fill="#2c3e50" font-size="10" font-family="Arial">Exchange Rates per Location (latest shown)</text>

  <text x="900" y="775" text-anchor="middle" fill="white" font-size="12" font-weight="bold" font-family="Arial">D3: Balances</text>

  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50"/>
    </marker>
  </defs>

  <!-- Staff to Authentication -->
  <path d="M 200 140 L 290 220" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="245" y="175" fill="#2c3e50" font-size="9" font-family="Arial">Login Credentials</text>

  <!-- Authentication to Staff -->
  <path d="M 310 210 L 180 160" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="220" y="190" fill="#2c3e50" font-size="9" font-family="Arial">Access Token</text>

  <!-- Staff to Transaction Processing -->
  <path d="M 200 140 L 630 280" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="415" y="210" fill="#2c3e50" font-size="9" font-family="Arial">Transaction Data</text>

  <!-- Transaction Processing to Balance Calculation -->
  <path d="M 770 300 L 985 350" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="875" y="320" fill="#2c3e50" font-size="9" font-family="Arial">Balance Update</text>

  <!-- Data Store Connections -->
  <path d="M 350 310 L 400 750" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="360" y="530" fill="#e74c3c" font-size="9" font-family="Arial">User Validation</text>

  <path d="M 700 370 L 650 750" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="660" y="560" fill="#e74c3c" font-size="9" font-family="Arial">Store Transaction</text>

  <path d="M 1050 415 L 900 750" stroke="#e74c3c" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="960" y="580" fill="#e74c3c" font-size="9" font-family="Arial">Update Balance</text>

  <!-- Legend -->
  <rect x="50" y="900" width="500" height="80" fill="white" stroke="#bdc3c7" stroke-width="1" rx="5" filter="url(#shadow)"/>
  <text x="300" y="920" text-anchor="middle" fill="#2c3e50" font-size="14" font-weight="bold" font-family="Arial">Data Flow Legend</text>

  <rect x="70" y="930" width="40" height="20" fill="url(#externalGrad)" stroke="#229954" stroke-width="1" rx="3"/>
  <text x="120" y="945" fill="#2c3e50" font-size="10" font-family="Arial">External Entity</text>

  <circle cx="250" cy="940" r="15" fill="url(#processGrad)" stroke="#2980b9" stroke-width="1"/>
  <text x="275" y="945" fill="#2c3e50" font-size="10" font-family="Arial">Process</text>

  <rect x="350" y="930" width="60" height="20" fill="url(#dataGrad)" stroke="#c0392b" stroke-width="1" rx="3"/>
  <text x="420" y="945" fill="#2c3e50" font-size="10" font-family="Arial">Data Store</text>

  <path d="M 70 960 L 110 960" stroke="#2c3e50" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  <text x="120" y="965" fill="#2c3e50" font-size="10" font-family="Arial">Data Flow</text>
</svg>
