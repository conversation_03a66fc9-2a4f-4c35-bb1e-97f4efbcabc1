"""
Enhanced Exchange Rate Management System for Arena Doviz.
Provides automatic rate updates, validation, approval workflows, and notifications.
"""

from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import timedelta
from .models import Currency, ExchangeRate, ExchangeRateHistory
from apps.core.utils import log_user_action
from .notifications import rate_notification_manager
import logging
import requests
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class ExchangeRateManager:
    """
    Comprehensive exchange rate management with automatic updates and validation.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.max_rate_change_percentage = Decimal('10.0')  # 10% max change
        self.rate_validation_threshold = Decimal('5.0')  # 5% warning threshold
    
    def create_rate_with_approval(self, rate_data, created_by, requires_approval=True):
        """
        Create a new exchange rate with optional approval workflow.
        
        Args:
            rate_data: Dictionary containing rate information
            created_by: User creating the rate
            requires_approval: Whether the rate requires approval
            
        Returns:
            tuple: (ExchangeRate object, approval_required)
        """
        try:
            with transaction.atomic():
                # Validate rate data
                validation_result = self.validate_rate_change(rate_data)
                
                # Determine if approval is required
                approval_required = (
                    requires_approval or 
                    validation_result['requires_approval'] or
                    validation_result['significant_change']
                )
                
                # Create the rate
                rate = ExchangeRate.objects.create(
                    from_currency=rate_data['from_currency'],
                    to_currency=rate_data['to_currency'],
                    location=rate_data['location'],
                    buy_rate=rate_data['buy_rate'],
                    sell_rate=rate_data['sell_rate'],
                    effective_from=rate_data.get('effective_from', timezone.now()),
                    source=rate_data.get('source', 'manual'),
                    notes=rate_data.get('notes', ''),
                    is_active=not approval_required,  # Inactive if approval required
                    created_by=created_by
                )
                
                # Log the creation
                log_user_action(
                    user=created_by,
                    action='create_exchange_rate',
                    model_name='ExchangeRate',
                    object_id=str(rate.id),
                    object_repr=str(rate),
                    additional_data={
                        'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                        'location': rate.location.code,
                        'buy_rate': str(rate.buy_rate),
                        'sell_rate': str(rate.sell_rate),
                        'approval_required': approval_required,
                        'validation_warnings': validation_result.get('warnings', [])
                    }
                )
                
                # Send notifications if approval is required
                if approval_required:
                    rate_notification_manager.send_rate_approval_request(rate, created_by)
                
                self.logger.info(f"Exchange rate created: {rate} (approval_required: {approval_required})")
                
                return rate, approval_required
                
        except Exception as e:
            self.logger.error(f"Error creating exchange rate: {str(e)}")
            raise
    
    def approve_rate(self, rate_id, approved_by, notes=None):
        """
        Approve a pending exchange rate.
        
        Args:
            rate_id: ID of the rate to approve
            approved_by: User approving the rate
            notes: Optional approval notes
            
        Returns:
            ExchangeRate: The approved rate
        """
        try:
            with transaction.atomic():
                rate = ExchangeRate.objects.get(id=rate_id, is_deleted=False)
                
                # Check permissions
                if not approved_by.can_approve_transactions():
                    raise ValidationError("User does not have permission to approve rates")
                
                # Check if rate is already active
                if rate.is_active:
                    raise ValidationError("Rate is already approved and active")
                
                # Deactivate existing current rate for the same currency pair and location
                existing_rate = ExchangeRate.objects.filter(
                    from_currency=rate.from_currency,
                    to_currency=rate.to_currency,
                    location=rate.location,
                    is_active=True,
                    is_deleted=False,
                    effective_until__isnull=True
                ).first()
                
                if existing_rate:
                    existing_rate.effective_until = rate.effective_from
                    existing_rate.save(update_fields=['effective_until'])
                    
                    # Create history entry
                    ExchangeRateHistory.objects.create(
                        exchange_rate=rate,
                        old_buy_rate=existing_rate.buy_rate,
                        new_buy_rate=rate.buy_rate,
                        old_sell_rate=existing_rate.sell_rate,
                        new_sell_rate=rate.sell_rate,
                        changed_by=approved_by,
                        reason=f"Rate approved and activated (replaced rate ID: {existing_rate.id})"
                    )
                
                # Activate the new rate
                rate.is_active = True
                rate.approved_by = approved_by
                rate.approved_at = timezone.now()
                if notes:
                    rate.notes = f"{rate.notes}\nApproval notes: {notes}" if rate.notes else f"Approval notes: {notes}"
                rate.save()
                
                # Log the approval
                log_user_action(
                    user=approved_by,
                    action='approve_exchange_rate',
                    resource_type='ExchangeRate',
                    resource_id=str(rate.id),
                    additional_data={
                        'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                        'location': rate.location.code,
                        'approval_notes': notes
                    }
                )
                
                # Send approval notification
                rate_notification_manager.send_rate_approved_notification(rate, approved_by)
                
                self.logger.info(f"Exchange rate approved: {rate} by {approved_by}")
                
                return rate
                
        except Exception as e:
            self.logger.error(f"Error approving exchange rate {rate_id}: {str(e)}")
            raise
    
    def reject_rate(self, rate_id, rejected_by, reason):
        """
        Reject a pending exchange rate.
        
        Args:
            rate_id: ID of the rate to reject
            rejected_by: User rejecting the rate
            reason: Reason for rejection
        """
        try:
            with transaction.atomic():
                rate = ExchangeRate.objects.get(id=rate_id, is_deleted=False)
                
                # Check permissions
                if not rejected_by.can_approve_transactions():
                    raise ValidationError("User does not have permission to reject rates")
                
                # Soft delete the rate
                rate.is_deleted = True
                rate.notes = f"{rate.notes}\nRejected: {reason}" if rate.notes else f"Rejected: {reason}"
                rate.save()
                
                # Log the rejection
                log_user_action(
                    user=rejected_by,
                    action='reject_exchange_rate',
                    resource_type='ExchangeRate',
                    resource_id=str(rate.id),
                    additional_data={
                        'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                        'location': rate.location.code,
                        'rejection_reason': reason
                    }
                )
                
                # Send rejection notification
                rate_notification_manager.send_rate_rejected_notification(rate, rejected_by, reason)
                
                self.logger.info(f"Exchange rate rejected: {rate} by {rejected_by}")
                
        except Exception as e:
            self.logger.error(f"Error rejecting exchange rate {rate_id}: {str(e)}")
            raise
    
    def validate_rate_change(self, rate_data):
        """
        Validate exchange rate changes and determine if approval is required.
        
        Args:
            rate_data: Dictionary containing rate information
            
        Returns:
            dict: Validation result with warnings and approval requirements
        """
        result = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'requires_approval': False,
            'significant_change': False
        }
        
        try:
            # Get current rate for comparison
            current_rate = ExchangeRate.get_current_rate(
                rate_data['from_currency'],
                rate_data['to_currency'],
                rate_data['location']
            )
            
            if current_rate:
                # Calculate percentage changes
                buy_change = self._calculate_percentage_change(
                    current_rate.buy_rate, rate_data['buy_rate']
                )
                sell_change = self._calculate_percentage_change(
                    current_rate.sell_rate, rate_data['sell_rate']
                )
                
                # Check for significant changes
                if abs(buy_change) > self.rate_validation_threshold:
                    result['warnings'].append(f"Buy rate change: {buy_change:.2f}%")
                    if abs(buy_change) > self.max_rate_change_percentage:
                        result['requires_approval'] = True
                        result['significant_change'] = True
                
                if abs(sell_change) > self.rate_validation_threshold:
                    result['warnings'].append(f"Sell rate change: {sell_change:.2f}%")
                    if abs(sell_change) > self.max_rate_change_percentage:
                        result['requires_approval'] = True
                        result['significant_change'] = True
            
            # Validate spread
            spread_percentage = self._calculate_spread_percentage(
                rate_data['buy_rate'], rate_data['sell_rate']
            )
            
            if spread_percentage < Decimal('0.1'):  # Less than 0.1% spread
                result['warnings'].append(f"Very low spread: {spread_percentage:.3f}%")
            elif spread_percentage > Decimal('5.0'):  # More than 5% spread
                result['warnings'].append(f"High spread: {spread_percentage:.3f}%")
                result['requires_approval'] = True
            
            # Validate rate reasonableness (basic sanity checks)
            if rate_data['buy_rate'] <= 0 or rate_data['sell_rate'] <= 0:
                result['errors'].append("Rates must be positive")
                result['valid'] = False
            
            if rate_data['sell_rate'] <= rate_data['buy_rate']:
                result['errors'].append("Sell rate must be higher than buy rate")
                result['valid'] = False
            
        except Exception as e:
            self.logger.error(f"Error validating rate change: {str(e)}")
            result['errors'].append(f"Validation error: {str(e)}")
            result['valid'] = False
        
        return result
    
    def get_rate_recommendations(self, from_currency, to_currency, location):
        """
        Get rate recommendations based on historical data and market trends.
        
        Args:
            from_currency: Source currency
            to_currency: Target currency
            location: Location for the rate
            
        Returns:
            dict: Rate recommendations with analysis
        """
        try:
            # Get historical rates for analysis
            historical_rates = ExchangeRate.objects.filter(
                from_currency=from_currency,
                to_currency=to_currency,
                location=location,
                is_deleted=False,
                effective_from__gte=timezone.now() - timedelta(days=30)
            ).order_by('-effective_from')[:10]
            
            if not historical_rates:
                return {
                    'recommendations': None,
                    'message': 'No historical data available for recommendations'
                }
            
            # Calculate averages and trends
            buy_rates = [rate.buy_rate for rate in historical_rates]
            sell_rates = [rate.sell_rate for rate in historical_rates]
            
            avg_buy = sum(buy_rates) / len(buy_rates)
            avg_sell = sum(sell_rates) / len(sell_rates)
            
            # Simple trend analysis (comparing recent vs older rates)
            recent_rates = historical_rates[:3]
            older_rates = historical_rates[3:6] if len(historical_rates) > 3 else historical_rates
            
            recent_avg_buy = sum(r.buy_rate for r in recent_rates) / len(recent_rates)
            older_avg_buy = sum(r.buy_rate for r in older_rates) / len(older_rates)
            
            trend = "stable"
            if recent_avg_buy > older_avg_buy * Decimal('1.02'):
                trend = "increasing"
            elif recent_avg_buy < older_avg_buy * Decimal('0.98'):
                trend = "decreasing"
            
            return {
                'recommendations': {
                    'suggested_buy_rate': avg_buy,
                    'suggested_sell_rate': avg_sell,
                    'trend': trend,
                    'confidence': 'medium' if len(historical_rates) >= 5 else 'low'
                },
                'analysis': {
                    'historical_count': len(historical_rates),
                    'avg_spread': avg_sell - avg_buy,
                    'avg_spread_percentage': ((avg_sell - avg_buy) / avg_buy * 100),
                    'volatility': 'low'  # Simplified volatility assessment
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting rate recommendations: {str(e)}")
            return {
                'recommendations': None,
                'error': str(e)
            }
    
    def _calculate_percentage_change(self, old_value, new_value):
        """Calculate percentage change between two values."""
        if old_value == 0:
            return Decimal('0')
        return ((new_value - old_value) / old_value) * 100
    
    def _calculate_spread_percentage(self, buy_rate, sell_rate):
        """Calculate spread percentage."""
        return ((sell_rate - buy_rate) / buy_rate) * 100
    
    def monitor_rate_changes(self, rate):
        """
        Monitor rate changes and send alerts for significant changes.

        Args:
            rate: New ExchangeRate object to monitor
        """
        try:
            # Get the previous rate for comparison
            previous_rate = ExchangeRate.objects.filter(
                from_currency=rate.from_currency,
                to_currency=rate.to_currency,
                location=rate.location,
                is_active=True,
                is_deleted=False,
                effective_until__isnull=False
            ).order_by('-effective_until').first()

            if previous_rate:
                # Calculate percentage changes
                buy_change = self._calculate_percentage_change(
                    previous_rate.buy_rate, rate.buy_rate
                )
                sell_change = self._calculate_percentage_change(
                    previous_rate.sell_rate, rate.sell_rate
                )

                # Send alerts for significant changes
                max_change = max(abs(buy_change), abs(sell_change))
                if max_change > self.rate_validation_threshold:
                    rate_notification_manager.send_rate_change_alert(
                        previous_rate, rate, max_change
                    )

                    self.logger.info(f"Rate change alert sent for {rate} ({max_change:.2f}% change)")

        except Exception as e:
            self.logger.error(f"Error monitoring rate changes for {rate.id}: {str(e)}")

    def get_rate_analytics(self, from_currency, to_currency, location, days=30):
        """
        Get analytics for a specific currency pair and location.

        Args:
            from_currency: Source currency
            to_currency: Target currency
            location: Location for the rate
            days: Number of days to analyze

        Returns:
            dict: Analytics data
        """
        try:
            from datetime import timedelta

            # Get historical rates
            start_date = timezone.now() - timedelta(days=days)
            historical_rates = ExchangeRate.objects.filter(
                from_currency=from_currency,
                to_currency=to_currency,
                location=location,
                is_deleted=False,
                effective_from__gte=start_date
            ).order_by('effective_from')

            if not historical_rates:
                return {'error': 'No historical data available'}

            # Calculate analytics
            buy_rates = [rate.buy_rate for rate in historical_rates]
            sell_rates = [rate.sell_rate for rate in historical_rates]

            analytics = {
                'period_days': days,
                'total_rate_changes': len(historical_rates),
                'buy_rate_stats': {
                    'min': min(buy_rates),
                    'max': max(buy_rates),
                    'avg': sum(buy_rates) / len(buy_rates),
                    'current': buy_rates[-1] if buy_rates else None
                },
                'sell_rate_stats': {
                    'min': min(sell_rates),
                    'max': max(sell_rates),
                    'avg': sum(sell_rates) / len(sell_rates),
                    'current': sell_rates[-1] if sell_rates else None
                },
                'volatility': self._calculate_volatility(buy_rates),
                'trend': self._analyze_trend(buy_rates)
            }

            return analytics

        except Exception as e:
            self.logger.error(f"Error calculating rate analytics: {str(e)}")
            return {'error': str(e)}

    def _calculate_volatility(self, rates):
        """Calculate simple volatility measure."""
        if len(rates) < 2:
            return 0

        changes = []
        for i in range(1, len(rates)):
            change = abs((rates[i] - rates[i-1]) / rates[i-1] * 100)
            changes.append(change)

        return sum(changes) / len(changes) if changes else 0

    def _analyze_trend(self, rates):
        """Analyze trend direction."""
        if len(rates) < 3:
            return 'insufficient_data'

        recent_avg = sum(rates[-3:]) / 3
        older_avg = sum(rates[:3]) / 3

        if recent_avg > older_avg * Decimal('1.02'):
            return 'increasing'
        elif recent_avg < older_avg * Decimal('0.98'):
            return 'decreasing'
        else:
            return 'stable'


class AutomaticRateUpdater:
    """
    Handles automatic exchange rate updates from external sources.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.rate_manager = ExchangeRateManager()
    
    def update_rates_from_external_source(self, source_name='central_bank'):
        """
        Update rates from external source (placeholder for future implementation).
        
        Args:
            source_name: Name of the external source
            
        Returns:
            dict: Update results
        """
        # This is a placeholder for external API integration
        # In a real implementation, this would:
        # 1. Fetch rates from central bank APIs
        # 2. Validate the rates
        # 3. Create pending rates for approval
        # 4. Send notifications
        
        self.logger.info(f"Automatic rate update from {source_name} (placeholder)")
        
        return {
            'success': False,
            'message': 'Automatic rate updates not yet implemented',
            'rates_updated': 0
        }
    
    def schedule_rate_updates(self):
        """Schedule automatic rate updates (placeholder for Celery integration)."""
        # This would integrate with Celery for scheduled tasks
        self.logger.info("Rate update scheduling (placeholder)")


# Global instances
rate_manager = ExchangeRateManager()
auto_updater = AutomaticRateUpdater()
