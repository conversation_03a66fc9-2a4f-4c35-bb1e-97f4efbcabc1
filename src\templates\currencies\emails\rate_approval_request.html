<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exchange Rate Approval Required</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .rate-details {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .rate-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .rate-row:last-child {
            border-bottom: none;
        }
        .label {
            font-weight: bold;
            color: #555;
        }
        .value {
            color: #333;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            font-weight: bold;
        }
        .button:hover {
            opacity: 0.9;
        }
        .button.secondary {
            background: #6c757d;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
        }
        .urgent {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏦 Arena Doviz Exchange</h1>
        <h2>Exchange Rate Approval Required</h2>
    </div>
    
    <div class="content">
        <p>Hello,</p>
        
        <p>A new exchange rate has been submitted and requires your approval:</p>
        
        <div class="rate-details">
            <h3>📊 Rate Details</h3>
            
            <div class="rate-row">
                <span class="label">Currency Pair:</span>
                <span class="value">{{ currency_pair }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Location:</span>
                <span class="value">{{ location }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Buy Rate:</span>
                <span class="value">{{ rate.buy_rate }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Sell Rate:</span>
                <span class="value">{{ rate.sell_rate }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Effective From:</span>
                <span class="value">{{ rate.effective_from|date:"M d, Y H:i" }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Created By:</span>
                <span class="value">{{ created_by.get_display_name }}</span>
            </div>
            
            <div class="rate-row">
                <span class="label">Source:</span>
                <span class="value">{{ rate.source|title }}</span>
            </div>
            
            {% if rate.notes %}
            <div class="rate-row">
                <span class="label">Notes:</span>
                <span class="value">{{ rate.notes }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="urgent">
            <strong>⚠️ Action Required:</strong> This rate is pending approval and will not be active until approved.
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ approval_url }}" class="button">
                ✅ Review & Approve
            </a>
            <a href="{{ approval_url }}" class="button secondary">
                📋 View Details
            </a>
        </div>
        
        <p><strong>Important:</strong> Please review the rate carefully before approving. Once approved, this rate will become active and replace any existing rate for this currency pair at this location.</p>
        
        <p>If you have any questions about this rate, please contact {{ created_by.get_display_name }} or your system administrator.</p>
    </div>
    
    <div class="footer">
        <p>This is an automated notification from Arena Doviz Exchange Accounting System.</p>
        <p>Generated on {{ timestamp|date:"M d, Y H:i T" }}</p>
        <p>Please do not reply to this email.</p>
    </div>
</body>
</html>
