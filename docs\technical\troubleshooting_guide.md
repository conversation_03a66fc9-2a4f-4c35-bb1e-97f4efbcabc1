# 🛠️ راهنمای عیب‌یابی Arena Doviz

این سند مشکلات رایج و راه‌حل‌های پیشنهادی را پوشش می‌دهد.

## ورود و احراز هویت
- مشکل: کاربر نمی‌تواند وارد شود
  - راه‌حل: بررسی ALLOWED_HOSTS، وضعیت دیتابیس، بررسی لاگ‌ها، ریست رمز توسط Admin

## معاملات
- مشکل: محاسبه کمیسیون نادرست
  - راه‌حل: بررسی تنظیمات کمیسیون (درصدی/ثابت) و نرخ‌های واردشده
- مشکل: عدم امکان بارگذاری رسید
  - راه‌حل: بررسی محدودیت اندازه فایل در Nginx و تنظیمات MEDIA

## نرخ ارز
- مشکل: نرخ آخرین مکان نمایش نمی‌شود
  - راه‌حل: اطمینان از ثبت نرخ برای مکان صحیح؛ بررسی endpoint های `/api/exchange-rates/latest/` و مکان انتخاب‌شده

## موجودی
- مشکل: موجودی منفی نمایش داده می‌شود
  - راه‌حل: بررسی سوابق تراکنش و فعال بودن تنظیم جلوگیری از منفی شدن؛ استفاده از drill-down برای ردیابی تراکنش‌های سازنده موجودی

## اعلان‌ها و واتساپ
- مشکل: پیام واتساپ ارسال نمی‌شود
  - راه‌حل: بررسی نصب WhatsApp Desktop؛ فعال بودن گزینه WHATSAPP_INTEGRATION=desktop؛ بررسی لاگ‌های سطح DEBUG ماژول اعلان

## استقرار و سرویس‌ها
- مشکل: 502 Bad Gateway در Nginx
  - راه‌حل: بررسی وضعیت Gunicorn روی پورت 8000؛ لاگ خطاهای Gunicorn؛ تنظیم timeouts
- مشکل: مهاجرت‌ها اعمال نشده است
  - راه‌حل: اجرای `python manage.py migrate` داخل کانتینر/سرور

## جمع‌آوری شواهد
- محل لاگ‌ها: `logs/app.log`، لاگ Nginx، لاگ سیستم
- فعال‌سازی موقت DEBUG برای بازتولید مشکل در محیط آزمایشی (هرگز در تولید)

## پشتیبانی
- برای مشکلات تکرارشونده، بلیت با جزئیات (زمان، کاربر، شناسه تراکنش) ثبت کنید.

