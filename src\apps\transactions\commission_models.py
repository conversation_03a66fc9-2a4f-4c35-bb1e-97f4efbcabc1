"""
Commission models for Arena Doviz Exchange Accounting System.
Handles comprehensive commission calculation logic for different transaction types.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from apps.core.models import BaseModel
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class CommissionRule(BaseModel):
    """
    Model for defining commission rules based on various criteria.
    Supports both percentage-based and fixed-amount commissions.
    """
    
    class CommissionType(models.TextChoices):
        PERCENTAGE = 'percentage', _('Percentage')
        FIXED_AMOUNT = 'fixed_amount', _('Fixed Amount')
        TIERED = 'tiered', _('Tiered')
        HYBRID = 'hybrid', _('Hybrid (Fixed + Percentage)')
    
    class ApplicableFor(models.TextChoices):
        ALL_TRANSACTIONS = 'all', _('All Transactions')
        CURRENCY_EXCHANGE = 'exchange', _('Currency Exchange')
        BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
        SWIFT_TRANSFER = 'swift', _('SWIFT Transfer')
        CASH_TRANSACTION = 'cash', _('Cash Transaction')
        INTERNAL_TRANSFER = 'internal', _('Internal Transfer')
        COURIER_DELIVERY = 'courier', _('Courier Delivery')
    
    # Basic information
    name = models.CharField(
        _('Rule name'),
        max_length=100,
        help_text=_('Descriptive name for this commission rule')
    )
    
    description = models.TextField(
        _('Description'),
        blank=True,
        help_text=_('Detailed description of when this rule applies')
    )
    
    # Rule criteria
    location = models.ForeignKey(
        'locations.Location',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Location'),
        help_text=_('Location where this rule applies (null for all locations)')
    )
    
    transaction_type = models.ForeignKey(
        'transactions.TransactionType',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_('Transaction type'),
        help_text=_('Transaction type this rule applies to (null for all types)')
    )
    
    applicable_for = models.CharField(
        _('Applicable for'),
        max_length=20,
        choices=ApplicableFor.choices,
        default=ApplicableFor.ALL_TRANSACTIONS,
        help_text=_('What type of transactions this rule applies to')
    )
    
    from_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.CASCADE,
        related_name='commission_rules_from',
        null=True,
        blank=True,
        verbose_name=_('From currency'),
        help_text=_('Source currency (null for any currency)')
    )
    
    to_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.CASCADE,
        related_name='commission_rules_to',
        null=True,
        blank=True,
        verbose_name=_('To currency'),
        help_text=_('Target currency (null for any currency)')
    )
    
    # Amount criteria
    min_amount = models.DecimalField(
        _('Minimum amount'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Minimum transaction amount for this rule to apply')
    )
    
    max_amount = models.DecimalField(
        _('Maximum amount'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Maximum transaction amount for this rule to apply')
    )
    
    # Commission calculation
    commission_type = models.CharField(
        _('Commission type'),
        max_length=20,
        choices=CommissionType.choices,
        default=CommissionType.PERCENTAGE,
        help_text=_('How commission is calculated')
    )
    
    percentage_rate = models.DecimalField(
        _('Percentage rate'),
        max_digits=5,
        decimal_places=4,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))],
        help_text=_('Commission percentage (e.g., 1.5 for 1.5%)')
    )
    
    fixed_amount = models.DecimalField(
        _('Fixed amount'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Fixed commission amount')
    )
    
    commission_currency = models.ForeignKey(
        'currencies.Currency',
        on_delete=models.PROTECT,
        related_name='commission_rules',
        null=True,
        blank=True,
        verbose_name=_('Commission currency'),
        help_text=_('Currency for commission (defaults to from_currency)')
    )
    
    # Rule settings
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this rule is currently active')
    )
    
    priority = models.PositiveIntegerField(
        _('Priority'),
        default=100,
        help_text=_('Rule priority (lower numbers have higher priority)')
    )
    
    effective_from = models.DateTimeField(
        _('Effective from'),
        null=True,
        blank=True,
        help_text=_('Date from which this rule is effective')
    )
    
    effective_until = models.DateTimeField(
        _('Effective until'),
        null=True,
        blank=True,
        help_text=_('Date until which this rule is effective')
    )
    
    class Meta:
        verbose_name = _('Commission Rule')
        verbose_name_plural = _('Commission Rules')
        ordering = ['priority', 'name']
        indexes = [
            models.Index(fields=['location', 'is_active']),
            models.Index(fields=['transaction_type', 'is_active']),
            models.Index(fields=['from_currency', 'to_currency']),
            models.Index(fields=['priority', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_commission_type_display()})"
    
    def clean(self):
        """Validate commission rule data."""
        super().clean()
        
        # Validate commission type specific fields
        if self.commission_type == self.CommissionType.PERCENTAGE:
            if not self.percentage_rate:
                raise ValidationError(_('Percentage rate is required for percentage commission'))
        elif self.commission_type == self.CommissionType.FIXED_AMOUNT:
            if not self.fixed_amount:
                raise ValidationError(_('Fixed amount is required for fixed amount commission'))
        elif self.commission_type == self.CommissionType.HYBRID:
            if not self.percentage_rate or not self.fixed_amount:
                raise ValidationError(_('Both percentage rate and fixed amount are required for hybrid commission'))
        
        # Validate amount range
        if self.min_amount and self.max_amount and self.min_amount > self.max_amount:
            raise ValidationError(_('Minimum amount cannot be greater than maximum amount'))
        
        # Validate effective dates
        if self.effective_from and self.effective_until and self.effective_from > self.effective_until:
            raise ValidationError(_('Effective from date cannot be after effective until date'))
    
    def applies_to_transaction(self, transaction):
        """
        Check if this commission rule applies to a given transaction.
        
        Args:
            transaction: Transaction object to check
            
        Returns:
            bool: True if rule applies, False otherwise
        """
        from django.utils import timezone
        
        # Check if rule is active
        if not self.is_active:
            return False
        
        # Check effective dates
        now = timezone.now()
        if self.effective_from and now < self.effective_from:
            return False
        if self.effective_until and now > self.effective_until:
            return False
        
        # Check location
        if self.location and self.location != transaction.location:
            return False
        
        # Check transaction type
        if self.transaction_type and self.transaction_type != transaction.transaction_type:
            return False
        
        # Check currencies
        if self.from_currency and self.from_currency != transaction.from_currency:
            return False
        if self.to_currency and self.to_currency != transaction.to_currency:
            return False
        
        # Check amount range
        if self.min_amount and transaction.from_amount < self.min_amount:
            return False
        if self.max_amount and transaction.from_amount > self.max_amount:
            return False
        
        # Check applicable transaction category
        if self.applicable_for != self.ApplicableFor.ALL_TRANSACTIONS:
            if not self._matches_transaction_category(transaction):
                return False
        
        return True
    
    def _matches_transaction_category(self, transaction):
        """Check if transaction matches the applicable category."""
        category_mapping = {
            self.ApplicableFor.CURRENCY_EXCHANGE: lambda t: t.is_exchange_transaction(),
            self.ApplicableFor.BANK_TRANSFER: lambda t: t.delivery_method == t.DeliveryMethod.BANK_TRANSFER,
            self.ApplicableFor.SWIFT_TRANSFER: lambda t: t.delivery_method == t.DeliveryMethod.SWIFT,
            self.ApplicableFor.CASH_TRANSACTION: lambda t: t.delivery_method == t.DeliveryMethod.CASH,
            self.ApplicableFor.INTERNAL_TRANSFER: lambda t: t.delivery_method == t.DeliveryMethod.INTERNAL,
            self.ApplicableFor.COURIER_DELIVERY: lambda t: t.delivery_method == t.DeliveryMethod.COURIER,
        }
        
        check_function = category_mapping.get(self.applicable_for)
        return check_function(transaction) if check_function else True
    
    def calculate_commission(self, transaction):
        """
        Calculate commission amount for a transaction based on this rule.
        
        Args:
            transaction: Transaction object
            
        Returns:
            tuple: (commission_amount, commission_currency)
        """
        if not self.applies_to_transaction(transaction):
            return Decimal('0'), None
        
        commission_currency = self.commission_currency or transaction.from_currency
        
        if self.commission_type == self.CommissionType.PERCENTAGE:
            commission_amount = transaction.from_amount * (self.percentage_rate / 100)
        elif self.commission_type == self.CommissionType.FIXED_AMOUNT:
            commission_amount = self.fixed_amount
        elif self.commission_type == self.CommissionType.HYBRID:
            percentage_commission = transaction.from_amount * (self.percentage_rate / 100)
            commission_amount = self.fixed_amount + percentage_commission
        else:
            commission_amount = Decimal('0')
        
        logger.info(f"Commission calculated for transaction {transaction.transaction_number}: "
                   f"{commission_currency.format_amount_with_symbol(commission_amount)} using rule {self.name}")
        
        return commission_amount, commission_currency


class CommissionTier(BaseModel):
    """
    Model for tiered commission structures.
    Allows different commission rates based on amount ranges.
    """
    
    commission_rule = models.ForeignKey(
        CommissionRule,
        on_delete=models.CASCADE,
        related_name='tiers',
        verbose_name=_('Commission rule'),
        help_text=_('The commission rule this tier belongs to')
    )
    
    min_amount = models.DecimalField(
        _('Minimum amount'),
        max_digits=15,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Minimum amount for this tier')
    )
    
    max_amount = models.DecimalField(
        _('Maximum amount'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Maximum amount for this tier (null for unlimited)')
    )
    
    percentage_rate = models.DecimalField(
        _('Percentage rate'),
        max_digits=5,
        decimal_places=4,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))],
        help_text=_('Commission percentage for this tier')
    )
    
    fixed_amount = models.DecimalField(
        _('Fixed amount'),
        max_digits=15,
        decimal_places=6,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0'))],
        help_text=_('Fixed commission amount for this tier')
    )
    
    class Meta:
        verbose_name = _('Commission Tier')
        verbose_name_plural = _('Commission Tiers')
        ordering = ['commission_rule', 'min_amount']
        unique_together = ['commission_rule', 'min_amount']
    
    def __str__(self):
        max_str = str(self.max_amount) if self.max_amount else '∞'
        return f"{self.commission_rule.name}: {self.min_amount} - {max_str}"
    
    def clean(self):
        """Validate tier data."""
        super().clean()
        
        if self.max_amount and self.min_amount > self.max_amount:
            raise ValidationError(_('Minimum amount cannot be greater than maximum amount'))
        
        if not self.percentage_rate and not self.fixed_amount:
            raise ValidationError(_('Either percentage rate or fixed amount must be specified'))
