"""
Exchange Rate Notification System for Arena Doviz.
Handles notifications for rate changes, approvals, and alerts.
"""

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import timezone
from apps.accounts.models import User
from apps.core.utils import log_user_action
import logging

logger = logging.getLogger(__name__)


class RateNotificationManager:
    """
    Manages notifications for exchange rate events.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def send_rate_approval_request(self, rate, created_by):
        """
        Send notification to approvers when a rate requires approval.
        
        Args:
            rate: ExchangeRate object requiring approval
            created_by: User who created the rate
        """
        try:
            # Get users who can approve rates
            approvers = User.objects.filter(
                role__in=[User.Role.ADMIN, User.Role.ACCOUNTANT],
                is_active=True,
                location=rate.location  # Only notify approvers in the same location
            )
            
            if not approvers.exists():
                self.logger.warning(f"No approvers found for rate {rate.id} at location {rate.location}")
                return
            
            # Prepare notification data
            context = {
                'rate': rate,
                'created_by': created_by,
                'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                'location': rate.location.name,
                'approval_url': f"{settings.FRONTEND_URL}/rates/approve/{rate.id}",
                'timestamp': timezone.now()
            }
            
            # Send email notifications
            for approver in approvers:
                self._send_email_notification(
                    recipient=approver,
                    subject=f"Exchange Rate Approval Required - {context['currency_pair']}",
                    template='currencies/emails/rate_approval_request.html',
                    context=context
                )
            
            # Send in-app notifications
            self._send_in_app_notifications(
                recipients=approvers,
                title="Rate Approval Required",
                message=f"New {context['currency_pair']} rate at {rate.location.name} requires approval",
                action_url=context['approval_url'],
                notification_type='rate_approval_request'
            )
            
            self.logger.info(f"Approval notifications sent for rate {rate.id} to {approvers.count()} approvers")
            
        except Exception as e:
            self.logger.error(f"Error sending approval request notifications for rate {rate.id}: {str(e)}")
    
    def send_rate_approved_notification(self, rate, approved_by):
        """
        Send notification when a rate is approved.
        
        Args:
            rate: Approved ExchangeRate object
            approved_by: User who approved the rate
        """
        try:
            # Notify the rate creator
            if rate.created_by and rate.created_by.is_active:
                context = {
                    'rate': rate,
                    'approved_by': approved_by,
                    'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                    'location': rate.location.name,
                    'rate_url': f"{settings.FRONTEND_URL}/rates/{rate.id}",
                    'timestamp': timezone.now()
                }
                
                self._send_email_notification(
                    recipient=rate.created_by,
                    subject=f"Exchange Rate Approved - {context['currency_pair']}",
                    template='currencies/emails/rate_approved.html',
                    context=context
                )
                
                self._send_in_app_notification(
                    recipient=rate.created_by,
                    title="Rate Approved",
                    message=f"Your {context['currency_pair']} rate has been approved by {approved_by.get_display_name()}",
                    action_url=context['rate_url'],
                    notification_type='rate_approved'
                )
            
            # Notify relevant users at the location
            location_users = User.objects.filter(
                location=rate.location,
                is_active=True,
                role__in=[User.Role.BRANCH_EMPLOYEE, User.Role.ACCOUNTANT]
            ).exclude(id=rate.created_by.id if rate.created_by else None)
            
            for user in location_users:
                self._send_in_app_notification(
                    recipient=user,
                    title="New Rate Active",
                    message=f"New {rate.from_currency.code}/{rate.to_currency.code} rate is now active",
                    action_url=f"{settings.FRONTEND_URL}/rates/{rate.id}",
                    notification_type='rate_activated'
                )
            
            self.logger.info(f"Approval notifications sent for rate {rate.id}")
            
        except Exception as e:
            self.logger.error(f"Error sending approval notifications for rate {rate.id}: {str(e)}")
    
    def send_rate_rejected_notification(self, rate, rejected_by, reason):
        """
        Send notification when a rate is rejected.
        
        Args:
            rate: Rejected ExchangeRate object
            rejected_by: User who rejected the rate
            reason: Reason for rejection
        """
        try:
            # Notify the rate creator
            if rate.created_by and rate.created_by.is_active:
                context = {
                    'rate': rate,
                    'rejected_by': rejected_by,
                    'reason': reason,
                    'currency_pair': f"{rate.from_currency.code}/{rate.to_currency.code}",
                    'location': rate.location.name,
                    'timestamp': timezone.now()
                }
                
                self._send_email_notification(
                    recipient=rate.created_by,
                    subject=f"Exchange Rate Rejected - {context['currency_pair']}",
                    template='currencies/emails/rate_rejected.html',
                    context=context
                )
                
                self._send_in_app_notification(
                    recipient=rate.created_by,
                    title="Rate Rejected",
                    message=f"Your {context['currency_pair']} rate was rejected: {reason}",
                    action_url=f"{settings.FRONTEND_URL}/rates/create",
                    notification_type='rate_rejected'
                )
            
            self.logger.info(f"Rejection notifications sent for rate {rate.id}")
            
        except Exception as e:
            self.logger.error(f"Error sending rejection notifications for rate {rate.id}: {str(e)}")
    
    def send_rate_change_alert(self, old_rate, new_rate, change_percentage):
        """
        Send alert for significant rate changes.
        
        Args:
            old_rate: Previous ExchangeRate object
            new_rate: New ExchangeRate object
            change_percentage: Percentage change
        """
        try:
            # Get users who should be notified of rate changes
            notification_users = User.objects.filter(
                location=new_rate.location,
                is_active=True,
                role__in=[User.Role.ADMIN, User.Role.ACCOUNTANT, User.Role.BRANCH_EMPLOYEE]
            )
            
            context = {
                'old_rate': old_rate,
                'new_rate': new_rate,
                'change_percentage': change_percentage,
                'currency_pair': f"{new_rate.from_currency.code}/{new_rate.to_currency.code}",
                'location': new_rate.location.name,
                'timestamp': timezone.now()
            }
            
            for user in notification_users:
                self._send_in_app_notification(
                    recipient=user,
                    title="Significant Rate Change",
                    message=f"{context['currency_pair']} rate changed by {change_percentage:.2f}%",
                    action_url=f"{settings.FRONTEND_URL}/rates/{new_rate.id}",
                    notification_type='rate_change_alert'
                )
            
            self.logger.info(f"Rate change alerts sent for {new_rate.id} ({change_percentage:.2f}% change)")
            
        except Exception as e:
            self.logger.error(f"Error sending rate change alerts: {str(e)}")
    
    def _send_email_notification(self, recipient, subject, template, context):
        """Send email notification to a user."""
        try:
            if not recipient.email:
                self.logger.warning(f"No email address for user {recipient.username}")
                return
            
            html_content = render_to_string(template, context)
            
            send_mail(
                subject=subject,
                message='',  # Plain text version (could be generated from HTML)
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient.email],
                html_message=html_content,
                fail_silently=False
            )
            
            self.logger.debug(f"Email sent to {recipient.email}: {subject}")
            
        except Exception as e:
            self.logger.error(f"Error sending email to {recipient.email}: {str(e)}")
    
    def _send_in_app_notification(self, recipient, title, message, action_url, notification_type):
        """Send in-app notification to a user."""
        try:
            # This would integrate with an in-app notification system
            # For now, we'll log the notification
            self.logger.info(f"In-app notification for {recipient.username}: {title} - {message}")
            
            # TODO: Implement actual in-app notification storage
            # This could be a Notification model that stores notifications for users
            
        except Exception as e:
            self.logger.error(f"Error sending in-app notification to {recipient.username}: {str(e)}")
    
    def _send_in_app_notifications(self, recipients, title, message, action_url, notification_type):
        """Send in-app notifications to multiple users."""
        for recipient in recipients:
            self._send_in_app_notification(recipient, title, message, action_url, notification_type)
    
    def send_whatsapp_notification(self, recipient, message):
        """
        Send WhatsApp notification (placeholder for future implementation).
        
        Args:
            recipient: User to send notification to
            message: Message content
        """
        try:
            # This would integrate with WhatsApp Business API
            # For now, just log the notification
            self.logger.info(f"WhatsApp notification for {recipient.username}: {message}")
            
            # TODO: Implement WhatsApp integration
            # This would use the WhatsApp Business API to send messages
            
        except Exception as e:
            self.logger.error(f"Error sending WhatsApp notification: {str(e)}")


# Global instance
rate_notification_manager = RateNotificationManager()
