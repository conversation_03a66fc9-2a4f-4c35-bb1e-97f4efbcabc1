# ⚙️ راهنمای پیکربندی سیستم Arena Doviz

این سند تنظیمات موردنیاز برای اجرا و تولید را توضیح می‌دهد.

## متغیرهای محیطی
- `SECRET_KEY`: کلید سری <PERSON> (ضروری)
- `DATABASE_URL`: اتصال PostgreSQL مانند: `******************************/exchange_db`
- `REDIS_URL`: آدرس Redis (اختیاری)
- `ALLOWED_HOSTS`: دامنه‌ها با کاما جدا
- `DEBUG`: false/true (در تولید false)
- `LANGUAGES`: مانند `fa,en`
- `DEFAULT_LANGUAGE`: `fa`
- `TIME_ZONE`: پیش‌فرض `Asia/Tehran`
- `WHATSAPP_INTEGRATION`: `desktop` (بازکردن WhatsApp Desktop)

## تنظیمات امنیتی
- فعال‌سازی HTTPS و HSTS
- تنظیم `SECURE_PROXY_SSL_HEADER` پشت Nginx
- محدودسازی اندازه آپلود (رسیدها/اسناد)
- رمزنگاری AES-256 برای داده‌های حساس (کلیدها در Secret Store)

## تنظیمات ماژول‌ها
### احراز هویت و مجوزها
- فعال‌سازی JWT برای API
- تعریف نقش‌ها: Admin, Accountant/Branch, Viewer
- مجوزها بر اساس نقش و مکان

### مشتریان
- فعال‌سازی ایجاد گروه واتساپ پس از ثبت (اختیاری)
- الگوی پیام اعلان تراکنش‌ها

### ارز و نرخ‌ها
- تعریف ارزهای اولیه: USD, AED, IRR
- تعریف مکان‌ها: Istanbul, Tabriz, Tehran, Dubai, China
- نرخ‌ها به تفکیک مکان؛ آخرین نرخ برای نمایش داشبورد

### معاملات
- پیکربندی کمیسیون: درصدی یا مبلغ ثابت
- فعال‌سازی اقساط چندمرحله‌ای
- تنظیمات الزامی بودن فیلدهای شماره پیگیری بانکی/داخلی
- مسیر ذخیره‌سازی فایل پیوست رسیدها

### گزارش‌گیری
- قالب‌های خروجی PDF/Excel
- نقشه‌نگاشت کدها: DBN, JV, TSN, TRQ, CBS

## فایل‌های پیکربندی نمونه
- `deployment/nginx.conf` (نمونه بلاک سرور)
- `deployment/docker-compose.yml` (اگر موجود)

## چک‌لیست قبل از تولید
- [ ] DEBUG=false
- [ ] SECRET_KEY امن
- [ ] ALLOWED_HOSTS تنظیم
- [ ] اتصال DB/Redis پایدار
- [ ] HTTPS فعال
- [ ] پشتیبان‌گیری زمان‌بندی‌شده

