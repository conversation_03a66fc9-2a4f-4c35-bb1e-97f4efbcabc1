"""
API views for Arena Doviz Core app.
Provides dashboard data and analytics endpoints.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count, Sum, Q, Avg
from django.utils import timezone
from datetime import timedelta, datetime
from decimal import Decimal
from apps.transactions.models import Transaction, BalanceEntry
from apps.customers.models import Customer
from apps.currencies.models import Currency
from apps.locations.models import Location
import logging

logger = logging.getLogger(__name__)


class DashboardViewSet(viewsets.ViewSet):
    """ViewSet for dashboard data and analytics."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def chart_data(self, request):
        """Get comprehensive chart data for dashboard."""
        try:
            # Get date range from query parameters
            days = int(request.query_params.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)
            
            # Filter by user location if not admin
            location_filter = Q()
            if not request.user.can_manage_users() and request.user.location:
                location_filter = Q(location=request.user.location)
            
            # Get transaction volume data
            transaction_volume = self._get_transaction_volume_data(start_date, end_date, location_filter)
            
            # Get currency distribution data
            currency_distribution = self._get_currency_distribution_data(start_date, end_date, location_filter)
            
            # Get profit analysis data
            profit_analysis = self._get_profit_analysis_data(start_date, end_date, location_filter)
            
            # Get balance trends data
            balance_trends = self._get_balance_trends_data(start_date, end_date, location_filter)
            
            # Get transaction status distribution
            status_distribution = self._get_status_distribution_data(start_date, end_date, location_filter)
            
            # Get location performance data (for admins)
            location_performance = None
            if request.user.can_manage_users():
                location_performance = self._get_location_performance_data(start_date, end_date)
            
            return Response({
                'transaction_volume': transaction_volume,
                'currency_distribution': currency_distribution,
                'profit_analysis': profit_analysis,
                'balance_trends': balance_trends,
                'status_distribution': status_distribution,
                'location_performance': location_performance,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                }
            })
            
        except Exception as e:
            logger.error(f"Error getting chart data: {str(e)}")
            return Response(
                {'error': 'Failed to load chart data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def export_data(self, request):
        """Export analytics data in CSV format."""
        try:
            from django.http import HttpResponse
            import csv
            from io import StringIO

            # Get date range from query parameters
            days = int(request.query_params.get('days', 30))
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=days)

            # Filter by user location if not admin
            location_filter = Q()
            if not request.user.can_manage_users() and request.user.location:
                location_filter = Q(location=request.user.location)

            # Get transaction data
            transactions = Transaction.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date,
                is_deleted=False
            ).filter(location_filter).select_related(
                'customer', 'location', 'from_currency', 'to_currency', 'transaction_type'
            ).order_by('-created_at')

            # Create CSV response
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="arena_doviz_analytics_{start_date}_{end_date}.csv"'

            writer = csv.writer(response)

            # Write header
            writer.writerow([
                'Transaction Number',
                'Date',
                'Customer',
                'Location',
                'Type',
                'From Currency',
                'To Currency',
                'From Amount',
                'To Amount',
                'Exchange Rate',
                'Commission Amount',
                'Status',
                'Created By'
            ])

            # Write data
            for transaction in transactions:
                writer.writerow([
                    transaction.transaction_number,
                    transaction.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    transaction.customer.get_display_name() if transaction.customer else 'N/A',
                    transaction.location.name if transaction.location else 'N/A',
                    transaction.transaction_type.name if transaction.transaction_type else 'N/A',
                    transaction.from_currency.code if transaction.from_currency else 'N/A',
                    transaction.to_currency.code if transaction.to_currency else 'N/A',
                    str(transaction.from_amount),
                    str(transaction.to_amount),
                    str(transaction.exchange_rate),
                    str(transaction.commission_amount or 0),
                    transaction.get_status_display(),
                    transaction.created_by.username if transaction.created_by else 'N/A'
                ])

            logger.info(f"Analytics data exported by user: {request.user.username}")

            return response

        except Exception as e:
            logger.error(f"Error exporting analytics data: {str(e)}")
            return Response(
                {'error': 'Failed to export data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def _get_transaction_volume_data(self, start_date, end_date, location_filter):
        """Get daily transaction volume data."""
        transactions = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            is_deleted=False
        ).filter(location_filter)
        
        # Group by date
        daily_data = {}
        current_date = start_date
        while current_date <= end_date:
            daily_data[current_date.isoformat()] = {
                'date': current_date.isoformat(),
                'count': 0,
                'volume': 0,
                'commission': 0
            }
            current_date += timedelta(days=1)
        
        # Aggregate transaction data
        for transaction in transactions:
            date_key = transaction.created_at.date().isoformat()
            if date_key in daily_data:
                daily_data[date_key]['count'] += 1
                daily_data[date_key]['volume'] += float(transaction.from_amount)
                daily_data[date_key]['commission'] += float(transaction.commission_amount or 0)
        
        return {
            'labels': list(daily_data.keys()),
            'datasets': [
                {
                    'label': 'Transaction Count',
                    'data': [data['count'] for data in daily_data.values()],
                    'type': 'line',
                    'borderColor': 'rgb(75, 192, 192)',
                    'backgroundColor': 'rgba(75, 192, 192, 0.1)',
                    'yAxisID': 'y'
                },
                {
                    'label': 'Volume (USD)',
                    'data': [data['volume'] for data in daily_data.values()],
                    'type': 'bar',
                    'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                    'borderColor': 'rgb(54, 162, 235)',
                    'yAxisID': 'y1'
                }
            ]
        }
    
    def _get_currency_distribution_data(self, start_date, end_date, location_filter):
        """Get currency distribution data."""
        currency_data = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        ).filter(location_filter).values(
            'from_currency__code', 'from_currency__name'
        ).annotate(
            count=Count('id'),
            total_amount=Sum('from_amount')
        ).order_by('-total_amount')
        
        labels = []
        data = []
        colors = [
            'rgb(255, 99, 132)',
            'rgb(54, 162, 235)',
            'rgb(255, 205, 86)',
            'rgb(75, 192, 192)',
            'rgb(153, 102, 255)',
            'rgb(255, 159, 64)'
        ]
        
        for i, item in enumerate(currency_data):
            labels.append(item['from_currency__code'])
            data.append(float(item['total_amount']))
        
        return {
            'labels': labels,
            'datasets': [{
                'data': data,
                'backgroundColor': colors[:len(data)],
                'borderWidth': 2
            }]
        }
    
    def _get_profit_analysis_data(self, start_date, end_date, location_filter):
        """Get profit analysis data."""
        transactions = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        ).filter(location_filter)
        
        # Group by date for profit trends
        daily_profit = {}
        current_date = start_date
        while current_date <= end_date:
            daily_profit[current_date.isoformat()] = 0
            current_date += timedelta(days=1)
        
        total_commission = 0
        for transaction in transactions:
            date_key = transaction.created_at.date().isoformat()
            commission = float(transaction.commission_amount or 0)
            if date_key in daily_profit:
                daily_profit[date_key] += commission
            total_commission += commission
        
        return {
            'labels': list(daily_profit.keys()),
            'datasets': [{
                'label': 'Daily Commission',
                'data': list(daily_profit.values()),
                'borderColor': 'rgb(255, 99, 132)',
                'backgroundColor': 'rgba(255, 99, 132, 0.1)',
                'fill': True
            }],
            'total_commission': total_commission
        }
    
    def _get_balance_trends_data(self, start_date, end_date, location_filter):
        """Get balance trends data."""
        # Get balance data for major currencies
        currencies = Currency.objects.filter(is_active=True)[:3]  # Top 3 currencies
        
        datasets = []
        colors = ['rgb(75, 192, 192)', 'rgb(255, 99, 132)', 'rgb(255, 205, 86)']
        
        for i, currency in enumerate(currencies):
            # Get daily balance snapshots
            daily_balances = {}
            current_date = start_date
            while current_date <= end_date:
                # Calculate balance up to this date
                balance = BalanceEntry.get_current_balance(
                    customer=None,  # Company balance
                    location=None,  # All locations for admin, filtered for others
                    currency=currency
                )
                daily_balances[current_date.isoformat()] = float(balance)
                current_date += timedelta(days=1)
            
            datasets.append({
                'label': f'{currency.code} Balance',
                'data': list(daily_balances.values()),
                'borderColor': colors[i % len(colors)],
                'backgroundColor': colors[i % len(colors)].replace('rgb', 'rgba').replace(')', ', 0.1)'),
                'fill': False
            })
        
        return {
            'labels': list(daily_balances.keys()) if currencies else [],
            'datasets': datasets
        }
    
    def _get_status_distribution_data(self, start_date, end_date, location_filter):
        """Get transaction status distribution data."""
        status_data = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            is_deleted=False
        ).filter(location_filter).values('status').annotate(
            count=Count('id')
        ).order_by('-count')
        
        labels = []
        data = []
        colors = {
            'completed': 'rgb(40, 167, 69)',
            'pending': 'rgb(255, 193, 7)',
            'approved': 'rgb(0, 123, 255)',
            'draft': 'rgb(108, 117, 125)',
            'cancelled': 'rgb(220, 53, 69)',
            'rejected': 'rgb(220, 53, 69)'
        }
        
        for item in status_data:
            status_display = dict(Transaction.Status.choices)[item['status']]
            labels.append(status_display)
            data.append(item['count'])
        
        return {
            'labels': labels,
            'datasets': [{
                'data': data,
                'backgroundColor': [colors.get(status, 'rgb(108, 117, 125)') for status in [item['status'] for item in status_data]],
                'borderWidth': 2
            }]
        }
    
    def _get_location_performance_data(self, start_date, end_date):
        """Get location performance data (admin only)."""
        location_data = Transaction.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date,
            status=Transaction.Status.COMPLETED,
            is_deleted=False
        ).values(
            'location__name', 'location__code'
        ).annotate(
            count=Count('id'),
            total_volume=Sum('from_amount'),
            total_commission=Sum('commission_amount')
        ).order_by('-total_volume')
        
        labels = []
        volume_data = []
        commission_data = []
        
        for item in location_data:
            labels.append(item['location__name'] or 'Unknown')
            volume_data.append(float(item['total_volume'] or 0))
            commission_data.append(float(item['total_commission'] or 0))
        
        return {
            'labels': labels,
            'datasets': [
                {
                    'label': 'Transaction Volume',
                    'data': volume_data,
                    'backgroundColor': 'rgba(54, 162, 235, 0.5)',
                    'borderColor': 'rgb(54, 162, 235)',
                    'yAxisID': 'y'
                },
                {
                    'label': 'Commission Earned',
                    'data': commission_data,
                    'backgroundColor': 'rgba(255, 99, 132, 0.5)',
                    'borderColor': 'rgb(255, 99, 132)',
                    'yAxisID': 'y1'
                }
            ]
        }
