{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Transactions" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-arrow-left-right"></i>
                {% trans "Transactions" %}
            </h1>
            <a href="{% url 'transactions_web:add' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i>
                {% trans "New Transaction" %}
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="draft">{% trans "Draft" %}</option>
                            <option value="pending">{% trans "Pending" %}</option>
                            <option value="approved">{% trans "Approved" %}</option>
                            <option value="completed">{% trans "Completed" %}</option>
                            <option value="cancelled">{% trans "Cancelled" %}</option>
                            <option value="rejected">{% trans "Rejected" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="customer-filter" class="form-label">{% trans "Customer" %}</label>
                        <select class="form-select" id="customer-filter" name="customer">
                            <option value="">{% trans "All Customers" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="location-filter" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="location-filter" name="location">
                            <option value="">{% trans "All Locations" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="currency-filter" class="form-label">{% trans "Currency" %}</label>
                        <select class="form-select" id="currency-filter" name="currency">
                            <option value="">{% trans "All Currencies" %}</option>
                            <option value="USD">USD</option>
                            <option value="AED">AED</option>
                            <option value="IRR">IRR</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date-from" class="form-label">{% trans "Date From" %}</label>
                        <input type="date" class="form-control" id="date-from" name="date_from">
                    </div>
                    <div class="col-md-3">
                        <label for="date-to" class="form-label">{% trans "Date To" %}</label>
                        <input type="date" class="form-control" id="date-to" name="date_to">
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Transaction number, customer name...' %}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i>
                            {% trans "Filter" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "Transaction List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Customer" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Location" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">{% trans "Loading..." %}</span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="{% trans 'Transaction pagination' %}">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be populated by JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Actions Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">{% trans "Transaction Action" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="action-form">
                    <input type="hidden" id="action-transaction-id">
                    <input type="hidden" id="action-type">
                    
                    <div class="mb-3">
                        <label for="action-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="action-notes" name="notes" rows="3" placeholder="{% trans 'Optional notes for this action...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="confirm-action">{% trans "Confirm" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentFilters = {};

$(document).ready(function() {
    // Initialize page
    loadCustomers();
    loadLocations();
    loadTransactions();
    
    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadTransactions();
    });
    
    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        currentPage = 1;
        loadTransactions();
    });
    
    $('#refresh-btn').on('click', function() {
        loadTransactions();
    });
    
    $('#export-btn').on('click', function() {
        exportTransactions();
    });
    
    // Action modal handlers
    $('#confirm-action').on('click', function() {
        performTransactionAction();
    });
});

function loadCustomers() {
    $.ajax({
        url: '/api/v1/customers/customers/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            const select = $('#customer-filter');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(customer) {
                    select.append(`<option value="${customer.id}">${customer.display_name}</option>`);
                });
            }
        }
    });
}

function loadLocations() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            const select = $('#location-filter');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(location) {
                    select.append(`<option value="${location.id}">${location.name}</option>`);
                });
            }
        }
    });
}

function loadTransactions() {
    // Show loading
    const tbody = $('#transactions-table tbody');
    tbody.html(`
        <tr>
            <td colspan="8" class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{% trans "Loading..." %}</span>
                </div>
            </td>
        </tr>
    `);
    
    // Collect filters
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    params.append('page', currentPage);
    params.append('page_size', 20);
    
    $.ajax({
        url: '/api/v1/transactions/transactions/?' + params.toString(),
        method: 'GET',
        headers: {
            'Authorization': 'Token ' + getAuthToken()
        },
        success: function(data) {
            displayTransactions(data.results || []);
            updatePagination(data);
        },
        error: function() {
            tbody.html(`
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        {% trans "Error loading transactions" %}
                    </td>
                </tr>
            `);
        }
    });
}

function displayTransactions(transactions) {
    const tbody = $('#transactions-table tbody');
    tbody.empty();
    
    if (transactions.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">
                    {% trans "No transactions found" %}
                </td>
            </tr>
        `);
        return;
    }
    
    transactions.forEach(function(tx) {
        const statusClass = getStatusClass(tx.status);
        const formattedDate = new Date(tx.created_at).toLocaleString();
        
        const actions = generateActionButtons(tx);
        
        const row = `
            <tr>
                <td><code>${tx.transaction_number}</code></td>
                <td>${tx.customer_name}</td>
                <td>${tx.transaction_type_name}</td>
                <td>${tx.display_amount}</td>
                <td><span class="badge bg-${statusClass}">${tx.status_display}</span></td>
                <td>${tx.location_name}</td>
                <td>${formattedDate}</td>
                <td>${actions}</td>
            </tr>
        `;
        tbody.append(row);
    });
}

function generateActionButtons(transaction) {
    let buttons = `
        <a href="/transactions/${transaction.id}/" class="btn btn-sm btn-outline-primary me-1" title="{% trans 'View Details' %}">
            <i class="bi bi-eye"></i>
        </a>
    `;
    
    if (transaction.can_be_approved) {
        buttons += `
            <button class="btn btn-sm btn-outline-success me-1" onclick="showActionModal('${transaction.id}', 'approve')" title="{% trans 'Approve' %}">
                <i class="bi bi-check-circle"></i>
            </button>
        `;
    }
    
    if (transaction.status === 'approved') {
        buttons += `
            <button class="btn btn-sm btn-outline-info me-1" onclick="showActionModal('${transaction.id}', 'complete')" title="{% trans 'Complete' %}">
                <i class="bi bi-check2-circle"></i>
            </button>
        `;
    }
    
    if (transaction.can_be_cancelled) {
        buttons += `
            <button class="btn btn-sm btn-outline-danger" onclick="showActionModal('${transaction.id}', 'cancel')" title="{% trans 'Cancel' %}">
                <i class="bi bi-x-circle"></i>
            </button>
        `;
    }
    
    return buttons;
}

function showActionModal(transactionId, actionType) {
    $('#action-transaction-id').val(transactionId);
    $('#action-type').val(actionType);
    
    const titles = {
        'approve': '{% trans "Approve Transaction" %}',
        'complete': '{% trans "Complete Transaction" %}',
        'cancel': '{% trans "Cancel Transaction" %}',
        'reject': '{% trans "Reject Transaction" %}'
    };
    
    $('#actionModalTitle').text(titles[actionType]);
    $('#actionModal').modal('show');
}

function performTransactionAction() {
    const transactionId = $('#action-transaction-id').val();
    const actionType = $('#action-type').val();
    const notes = $('#action-notes').val();
    
    $.ajax({
        url: `/api/v1/transactions/transactions/${transactionId}/${actionType}/`,
        method: 'POST',
        headers: {
            'Authorization': 'Token ' + getAuthToken(),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({ notes: notes }),
        success: function(data) {
            $('#actionModal').modal('hide');
            loadTransactions();
            showAlert('success', data.message || '{% trans "Action completed successfully" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '{% trans "Action failed" %}';
            showAlert('danger', error);
        }
    });
}

function updatePagination(data) {
    const pagination = $('#pagination');
    pagination.empty();
    
    if (!data.count || data.count <= 20) {
        return;
    }
    
    const totalPages = Math.ceil(data.count / 20);
    
    // Previous button
    if (data.previous) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">{% trans "Previous" %}</a>
            </li>
        `);
    }
    
    // Page numbers
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const activeClass = i === currentPage ? 'active' : '';
        pagination.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `);
    }
    
    // Next button
    if (data.next) {
        pagination.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">{% trans "Next" %}</a>
            </li>
        `);
    }
}

function changePage(page) {
    currentPage = page;
    loadTransactions();
}

function exportTransactions() {
    // This would trigger a download of the filtered transactions
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    params.append('format', 'excel');
    
    window.open('/api/v1/transactions/transactions/export/?' + params.toString());
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
