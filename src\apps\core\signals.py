"""
Signal handlers for Arena Doviz Core app.
"""

from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
import logging

logger = logging.getLogger(__name__)

User = get_user_model()


@receiver(post_save, sender=User)
def log_user_creation(sender, instance, created, **kwargs):
    """Log user creation events."""
    if created:
        logger.info(f"New user created: {instance.username} with role {instance.role}")


@receiver(post_delete, sender=User)
def log_user_deletion(sender, instance, **kwargs):
    """Log user deletion events."""
    logger.warning(f"User deleted: {instance.username}")


# Additional signal handlers can be added here as needed
