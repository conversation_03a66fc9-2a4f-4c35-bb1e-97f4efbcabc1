{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Login" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.alert {
    border-radius: 8px;
    border: none;
}

.loading-spinner {
    display: none;
}

.form-check {
    margin: 1rem 0;
}

.forgot-password {
    text-align: center;
    margin-top: 1rem;
}

.forgot-password a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password a:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block navbar %}
<!-- Hide navbar on login page -->
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>{% trans "Welcome Back" %}</h2>
            <p>{% trans "Sign in to your Arena Doviz account" %}</p>
        </div>

        <div id="login-alerts"></div>

        <form id="login-form">
            {% csrf_token %}
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="{% trans 'Username' %}" required>
                <label for="username">{% trans "Username" %}</label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="{% trans 'Password' %}" required>
                <label for="password">{% trans "Password" %}</label>
            </div>

            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                <label class="form-check-label" for="remember_me">
                    {% trans "Remember me" %}
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-login w-100">
                <span class="login-text">{% trans "Sign In" %}</span>
                <span class="loading-spinner">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    {% trans "Signing in..." %}
                </span>
            </button>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">{% trans "Forgot your password?" %}</a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check if user is already authenticated
    if (ArenaDoviz.auth.isAuthenticated()) {
        window.location.href = '/dashboard/';
        return;
    }

    // Handle login form submission
    $('#login-form').on('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // Handle enter key in password field
    $('#password').on('keypress', function(e) {
        if (e.which === 13) {
            handleLogin();
        }
    });
});

function handleLogin() {
    const username = $('#username').val().trim();
    const password = $('#password').val();
    const rememberMe = $('#remember_me').is(':checked');

    // Validate inputs
    if (!username || !password) {
        showAlert('error', '{% trans "Please enter both username and password" %}');
        return;
    }

    // Show loading state
    setLoadingState(true);
    clearAlerts();

    // Attempt JWT login
    ArenaDoviz.auth.login(username, password)
        .then(data => {
            showAlert('success', '{% trans "Login successful! Redirecting..." %}');
            
            // Redirect to dashboard after short delay
            setTimeout(() => {
                window.location.href = '/dashboard/';
            }, 1000);
        })
        .catch(error => {
            console.error('Login error:', error);
            setLoadingState(false);
            
            // Handle specific error messages
            if (error.message.includes('401')) {
                showAlert('error', '{% trans "Invalid username or password" %}');
            } else if (error.message.includes('locked')) {
                showAlert('error', '{% trans "Account is temporarily locked due to failed login attempts" %}');
            } else {
                showAlert('error', '{% trans "Login failed. Please try again." %}');
            }
        });
}

function setLoadingState(loading) {
    const button = $('.btn-login');
    const loginText = $('.login-text');
    const loadingSpinner = $('.loading-spinner');
    
    if (loading) {
        button.prop('disabled', true);
        loginText.hide();
        loadingSpinner.show();
    } else {
        button.prop('disabled', false);
        loginText.show();
        loadingSpinner.hide();
    }
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const iconClass = type === 'error' ? 'bi-exclamation-triangle' : 'bi-check-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="bi ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('#login-alerts').html(alertHtml);
}

function clearAlerts() {
    $('#login-alerts').empty();
}

function showForgotPassword() {
    // TODO: Implement forgot password functionality
    showAlert('info', '{% trans "Forgot password functionality will be available soon. Please contact your administrator." %}');
}

// Add CSS animation for spinner
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
