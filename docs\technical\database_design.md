# 🗄️ طراحی دیتابیس سیستم حسابداری صرافی

## 📋 نمای کلی

دیتابیس سیستم بر اساس اصول **دفترداری دوطرفه** (Double-Entry Bookkeeping) طراحی شده و از **PostgreSQL** استفاده می‌کند.

## 🏗️ ساختار کلی دیتابیس

### جداول اصلی (Core Tables)

#### 1. Users (کاربران)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(150) UNIQUE NOT NULL,
    email VARCHAR(254) UNIQUE,
    first_name VA<PERSON><PERSON><PERSON>(150),
    last_name VA<PERSON><PERSON><PERSON>(150),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    is_staff BOOLEAN DEFAULT FALSE,
    is_superuser BOOLEAN DEFAULT FALSE,
    date_joined TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    password_hash VARCHAR(128),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. User Roles (نقش‌های کاربری)
```sql
CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_role_assignments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    role_id INTEGER REFERENCES user_roles(id),
    assigned_at TIMESTAMP DEFAULT NOW(),
    assigned_by INTEGER REFERENCES users(id)
);
```

#### 3. Locations (مکان‌ها)
```sql
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager_id INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 4. Currencies (ارزها)
```sql
CREATE TABLE currencies (
    id SERIAL PRIMARY KEY,
    code VARCHAR(3) UNIQUE NOT NULL, -- USD, AED, IRR
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10),
    decimal_places INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 5. Exchange Rates (نرخ ارز)
```sql
CREATE TABLE exchange_rates (
    id SERIAL PRIMARY KEY,
    from_currency_id INTEGER REFERENCES currencies(id),
    to_currency_id INTEGER REFERENCES currencies(id),
    location_id INTEGER REFERENCES locations(id),
    buy_rate DECIMAL(15,6) NOT NULL,
    sell_rate DECIMAL(15,6) NOT NULL,
    rate_date DATE NOT NULL,
    entered_by INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(from_currency_id, to_currency_id, location_id, rate_date)
);
```

### جداول مشتریان و همکاران

#### 6. Customers (مشتریان)
```sql
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(150) NOT NULL,
    last_name VARCHAR(150) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(254),
    company_name VARCHAR(200),
    description TEXT,
    notes TEXT,
    whatsapp_group_id VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 7. Couriers (پیک‌ها)
```sql
CREATE TABLE couriers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### جداول معاملات

#### 8. Transaction Types (انواع معاملات)
```sql
CREATE TABLE transaction_types (
    id SERIAL PRIMARY KEY,
    code VARCHAR(10) UNIQUE NOT NULL, -- JV, TSN, TRQ, DBN, CBS
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### 9. Transactions (معاملات)
```sql
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    transaction_type_id INTEGER REFERENCES transaction_types(id),
    customer_id INTEGER REFERENCES customers(id),
    location_id INTEGER REFERENCES locations(id),
    currency_id INTEGER REFERENCES currencies(id),
    amount DECIMAL(15,6) NOT NULL,
    exchange_rate DECIMAL(15,6),
    commission_rate DECIMAL(5,4), -- percentage
    commission_amount DECIMAL(15,6),
    total_amount DECIMAL(15,6),
    description TEXT,
    notes TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, cancelled
    delivery_method VARCHAR(20), -- in_person, courier, bank_transfer, swift
    courier_id INTEGER REFERENCES couriers(id),
    tracking_code VARCHAR(100),
    bank_transaction_number VARCHAR(100),
    created_by INTEGER REFERENCES users(id),
    approved_by INTEGER REFERENCES users(id),
    transaction_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 10. Transaction Entries (قیدهای معاملات - دفترداری دوطرفه)
```sql
CREATE TABLE transaction_entries (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id),
    account_type VARCHAR(50) NOT NULL, -- customer_balance, company_balance, commission
    account_id INTEGER, -- customer_id or location_id
    currency_id INTEGER REFERENCES currencies(id),
    debit_amount DECIMAL(15,6) DEFAULT 0,
    credit_amount DECIMAL(15,6) DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### جداول موجودی

#### 11. Customer Balances (موجودی مشتریان)
```sql
CREATE TABLE customer_balances (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    currency_id INTEGER REFERENCES currencies(id),
    balance DECIMAL(15,6) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT NOW(),
    UNIQUE(customer_id, currency_id)
);
```

#### 12. Company Balances (موجودی شرکت)
```sql
CREATE TABLE company_balances (
    id SERIAL PRIMARY KEY,
    location_id INTEGER REFERENCES locations(id),
    currency_id INTEGER REFERENCES currencies(id),
    balance DECIMAL(15,6) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT NOW(),
    UNIQUE(location_id, currency_id)
);
```

### جداول فایل و مدارک

#### 13. Transaction Attachments (پیوست‌های معاملات)
```sql
CREATE TABLE transaction_attachments (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    description TEXT,
    uploaded_by INTEGER REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT NOW()
);
```

### جداول گزارش‌گیری

#### 14. Reports (گزارش‌ها)
```sql
CREATE TABLE reports (
    id SERIAL PRIMARY KEY,
    report_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    parameters JSONB,
    file_path VARCHAR(500),
    generated_by INTEGER REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);
```

### جداول تنظیمات

#### 15. System Settings (تنظیمات سیستم)
```sql
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    updated_by INTEGER REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 16. Audit Log (لاگ حسابرسی)
```sql
CREATE TABLE audit_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔗 روابط جداول

### روابط اصلی
- **Users ↔ Customers**: یک کاربر می‌تواند چندین مشتری ایجاد کند
- **Customers ↔ Transactions**: یک مشتری می‌تواند چندین معامله داشته باشد
- **Transactions ↔ Transaction Entries**: هر معامله چندین قید دارد
- **Locations ↔ Company Balances**: هر مکان موجودی مجزا دارد
- **Currencies ↔ Balances**: موجودی به تفکیک ارز

### کلیدهای خارجی
```sql
-- اضافه کردن کلیدهای خارجی
ALTER TABLE transactions ADD CONSTRAINT fk_transaction_customer 
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE transactions ADD CONSTRAINT fk_transaction_location 
    FOREIGN KEY (location_id) REFERENCES locations(id);

ALTER TABLE customer_balances ADD CONSTRAINT fk_balance_customer 
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE company_balances ADD CONSTRAINT fk_balance_location 
    FOREIGN KEY (location_id) REFERENCES locations(id);
```

## 📊 ایندکس‌ها

### ایندکس‌های عملکردی
```sql
-- ایندکس برای جستجوی سریع معاملات
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
CREATE INDEX idx_transactions_location ON transactions(location_id);
CREATE INDEX idx_transactions_status ON transactions(status);

-- ایندکس برای موجودی‌ها
CREATE INDEX idx_customer_balances_customer ON customer_balances(customer_id);
CREATE INDEX idx_company_balances_location ON company_balances(location_id);

-- ایندکس برای نرخ ارز
CREATE INDEX idx_exchange_rates_date ON exchange_rates(rate_date);
CREATE INDEX idx_exchange_rates_location ON exchange_rates(location_id);
```

### جداول فایل و مدارک

#### 13. Transaction Attachments (پیوست‌های معاملات)
```sql
CREATE TABLE transaction_attachments (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    description TEXT,
    uploaded_by INTEGER REFERENCES users(id),
    uploaded_at TIMESTAMP DEFAULT NOW()
);
```

### جداول گزارش‌گیری

#### 14. Reports (گزارش‌ها)
```sql
CREATE TABLE reports (
    id SERIAL PRIMARY KEY,
    report_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    parameters JSONB,
    file_path VARCHAR(500),
    generated_by INTEGER REFERENCES users(id),
    generated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);
```

### جداول تنظیمات

#### 15. System Settings (تنظیمات سیستم)
```sql
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    updated_by INTEGER REFERENCES users(id),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 16. Audit Log (لاگ حسابرسی)
```sql
CREATE TABLE audit_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    record_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    user_id INTEGER REFERENCES users(id),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔗 روابط جداول

### روابط اصلی
- **Users ↔ Customers**: یک کاربر می‌تواند چندین مشتری ایجاد کند
- **Customers ↔ Transactions**: یک مشتری می‌تواند چندین معامله داشته باشد
- **Transactions ↔ Transaction Entries**: هر معامله چندین قید دارد
- **Locations ↔ Company Balances**: هر مکان موجودی مجزا دارد
- **Currencies ↔ Balances**: موجودی به تفکیک ارز

### کلیدهای خارجی
```sql
-- اضافه کردن کلیدهای خارجی
ALTER TABLE transactions ADD CONSTRAINT fk_transaction_customer
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE transactions ADD CONSTRAINT fk_transaction_location
    FOREIGN KEY (location_id) REFERENCES locations(id);

ALTER TABLE customer_balances ADD CONSTRAINT fk_balance_customer
    FOREIGN KEY (customer_id) REFERENCES customers(id);

ALTER TABLE company_balances ADD CONSTRAINT fk_balance_location
    FOREIGN KEY (location_id) REFERENCES locations(id);
```

## 📊 ایندکس‌ها

### ایندکس‌های عملکردی
```sql
-- ایندکس برای جستجوی سریع معاملات
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
CREATE INDEX idx_transactions_location ON transactions(location_id);
CREATE INDEX idx_transactions_status ON transactions(status);

-- ایندکس برای موجودی‌ها
CREATE INDEX idx_customer_balances_customer ON customer_balances(customer_id);
CREATE INDEX idx_company_balances_location ON company_balances(location_id);

-- ایندکس برای نرخ ارز
CREATE INDEX idx_exchange_rates_date ON exchange_rates(rate_date);
CREATE INDEX idx_exchange_rates_location ON exchange_rates(location_id);
```

## 🔐 امنیت دیتابیس

### کنترل دسترسی
```sql
-- ایجاد نقش‌های دیتابیس
CREATE ROLE exchange_admin;
CREATE ROLE exchange_user;
CREATE ROLE exchange_readonly;

-- تخصیص مجوزها
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO exchange_admin;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO exchange_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO exchange_readonly;
```

### رمزنگاری
- **Sensitive Data**: رمزنگاری فیلدهای حساس با AES-256
- **Password Hashing**: استفاده از bcrypt برای رمزهای عبور
- **Connection Encryption**: اتصال رمزنگاری شده به دیتابیس

## 🔄 پشتیبان‌گیری

### استراتژی پشتیبان‌گیری
```sql
-- پشتیبان‌گیری روزانه
pg_dump -h localhost -U postgres -d exchange_db > backup_$(date +%Y%m%d).sql

-- پشتیبان‌گیری فشرده
pg_dump -h localhost -U postgres -d exchange_db | gzip > backup_$(date +%Y%m%d).sql.gz
```

### بازیابی
```sql
-- بازیابی از پشتیبان
psql -h localhost -U postgres -d exchange_db < backup_20241201.sql
```

## 📈 بهینه‌سازی

### تنظیمات PostgreSQL
```sql
-- تنظیمات عملکرد
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

### مانیتورینگ
- **Query Performance**: مانیتورینگ کوئری‌های کند
- **Index Usage**: بررسی استفاده از ایندکس‌ها
- **Table Statistics**: آمار جداول و بهینه‌سازی

## 🔄 پشتیبان‌گیری

### استراتژی پشتیبان‌گیری
```sql
-- پشتیبان‌گیری روزانه
pg_dump -h localhost -U postgres -d exchange_db > backup_$(date +%Y%m%d).sql

-- پشتیبان‌گیری فشرده
pg_dump -h localhost -U postgres -d exchange_db | gzip > backup_$(date +%Y%m%d).sql.gz
```

### بازیابی
```sql
-- بازیابی از پشتیبان
psql -h localhost -U postgres -d exchange_db < backup_20241201.sql
```

## 📈 بهینه‌سازی

### تنظیمات PostgreSQL
```sql
-- تنظیمات عملکرد
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

### مانیتورینگ
- **Query Performance**: مانیتورینگ کوئری‌های کند
- **Index Usage**: بررسی استفاده از ایندکس‌ها
- **Table Statistics**: آمار جداول و بهینه‌سازی
