server {
    listen 80;
    server_name _;

    client_max_body_size 20m;

    location /static/ {
        alias /var/www/arena/static/;
    }

    location /media/ {
        alias /var/www/arena/media/;
    }

    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

