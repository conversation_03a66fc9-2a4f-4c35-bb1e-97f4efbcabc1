"""
API URL configuration for Arena Doviz Accounts app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
    TokenBlacklistView
)
from .views import UserViewSet, UserSessionViewSet, AuditLogViewSet

app_name = 'accounts'

# API router for accounts endpoints
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'sessions', UserSessionViewSet)
router.register(r'audit-logs', AuditLogViewSet)

urlpatterns = [
    # JWT Authentication endpoints
    path('jwt/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('jwt/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('jwt/token/verify/', TokenVerifyView.as_view(), name='token_verify'),
    path('jwt/token/blacklist/', TokenBlacklistView.as_view(), name='token_blacklist'),

    # Standard API endpoints
    path('', include(router.urls)),
]
