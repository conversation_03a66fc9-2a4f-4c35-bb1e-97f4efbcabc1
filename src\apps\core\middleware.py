"""
Custom middleware for Arena Doviz Exchange Accounting System.
"""

import logging
import time
import json
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from .utils import get_client_ip, create_error_response, ArenaDovizException

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all requests and responses for audit purposes.
    """
    
    def process_request(self, request):
        """Log incoming requests."""
        request.start_time = time.time()
        
        # Get client information
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Log request details
        logger.info(
            f"Request: {request.method} {request.path}",
            extra={
                'method': request.method,
                'path': request.path,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                'query_params': dict(request.GET),
                'timestamp': timezone.now().isoformat()
            }
        )
        
        # Store request info for later use
        request.client_ip = ip_address
        request.user_agent = user_agent
    
    def process_response(self, request, response):
        """Log response details."""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info(
                f"Response: {request.method} {request.path} - {response.status_code} ({duration:.3f}s)",
                extra={
                    'method': request.method,
                    'path': request.path,
                    'status_code': response.status_code,
                    'duration': duration,
                    'ip_address': getattr(request, 'client_ip', 'unknown'),
                    'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                    'timestamp': timezone.now().isoformat()
                }
            )
        
        return response


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to handle exceptions and provide consistent error responses.
    """
    
    def process_exception(self, request, exception):
        """Handle exceptions and return appropriate responses."""
        
        # Log the exception
        logger.error(
            f"Exception in {request.method} {request.path}: {str(exception)}",
            extra={
                'method': request.method,
                'path': request.path,
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'ip_address': getattr(request, 'client_ip', 'unknown'),
                'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                'timestamp': timezone.now().isoformat()
            },
            exc_info=True
        )
        
        # Handle different types of exceptions
        if isinstance(exception, ArenaDovizException):
            return create_error_response(
                message=exception.message,
                error_code=exception.error_code,
                status_code=400,
                details=exception.details
            )
        
        elif isinstance(exception, PermissionDenied):
            return create_error_response(
                message="Permission denied",
                error_code="PERMISSION_DENIED",
                status_code=403
            )
        
        elif isinstance(exception, ValueError):
            return create_error_response(
                message="Invalid data provided",
                error_code="INVALID_DATA",
                status_code=400
            )
        
        # For API requests, return JSON error response
        if request.path.startswith('/api/') or request.content_type == 'application/json':
            return create_error_response(
                message="An internal error occurred",
                error_code="INTERNAL_ERROR",
                status_code=500
            )
        
        # For non-API requests, let Django handle it normally
        return None


class SecurityMiddleware(MiddlewareMixin):
    """
    Middleware to enhance security by checking various security constraints.
    """
    
    def process_request(self, request):
        """Perform security checks on incoming requests."""
        
        # Check for suspicious patterns
        suspicious_patterns = [
            'script',
            'javascript:',
            '<script',
            'eval(',
            'document.cookie',
            'union select',
            'drop table',
            '../',
            '..\\',
        ]
        
        # Check query parameters and form data
        all_params = {}
        all_params.update(request.GET.dict())
        if hasattr(request, 'POST'):
            all_params.update(request.POST.dict())
        
        for param, value in all_params.items():
            if isinstance(value, str):
                value_lower = value.lower()
                for pattern in suspicious_patterns:
                    if pattern in value_lower:
                        logger.warning(
                            f"Suspicious request detected: {pattern} in {param}",
                            extra={
                                'ip_address': get_client_ip(request),
                                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                                'path': request.path,
                                'suspicious_pattern': pattern,
                                'parameter': param,
                                'value': value[:100]  # Limit value length in logs
                            }
                        )
                        
                        return create_error_response(
                            message="Request blocked for security reasons",
                            error_code="SECURITY_VIOLATION",
                            status_code=400
                        )
        
        # Check for rate limiting (basic implementation)
        ip_address = get_client_ip(request)
        if self._is_rate_limited(request, ip_address):
            logger.warning(
                f"Rate limit exceeded for IP: {ip_address}",
                extra={
                    'ip_address': ip_address,
                    'path': request.path,
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')
                }
            )
            
            return create_error_response(
                message="Rate limit exceeded",
                error_code="RATE_LIMIT_EXCEEDED",
                status_code=429
            )
        
        return None
    
    def _is_rate_limited(self, request, ip_address):
        """
        Check if the request should be rate limited.
        This is a basic implementation - in production, use Redis or similar.
        """
        from django.core.cache import cache
        
        # Rate limit: 100 requests per minute per IP
        cache_key = f"rate_limit:{ip_address}"
        current_requests = cache.get(cache_key, 0)
        
        if current_requests >= 100:
            return True
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, 60)  # 60 seconds
        return False


class UserActivityMiddleware(MiddlewareMixin):
    """
    Middleware to track user activity and update last activity timestamp.
    """
    
    def process_request(self, request):
        """Update user's last activity."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # Update last activity timestamp
                request.user.last_login = timezone.now()
                request.user.save(update_fields=['last_login'])
                
                # Log user activity
                logger.debug(
                    f"User activity: {request.user.username} - {request.method} {request.path}",
                    extra={
                        'user_id': request.user.id,
                        'username': request.user.username,
                        'method': request.method,
                        'path': request.path,
                        'ip_address': get_client_ip(request)
                    }
                )
                
            except Exception as e:
                logger.error(f"Failed to update user activity: {e}")
        
        return None


class APIVersionMiddleware(MiddlewareMixin):
    """
    Middleware to handle API versioning.
    """
    
    def process_request(self, request):
        """Set API version from header or URL."""
        if request.path.startswith('/api/'):
            # Get version from header or URL
            api_version = request.META.get('HTTP_API_VERSION', 'v1')
            
            # Validate version
            supported_versions = ['v1']
            if api_version not in supported_versions:
                return create_error_response(
                    message=f"Unsupported API version: {api_version}",
                    error_code="UNSUPPORTED_API_VERSION",
                    status_code=400
                )
            
            request.api_version = api_version
        
        return None


class CORSMiddleware(MiddlewareMixin):
    """
    Custom CORS middleware for additional control.
    """
    
    def process_response(self, request, response):
        """Add CORS headers to response."""
        # Allow credentials
        response['Access-Control-Allow-Credentials'] = 'true'
        
        # Add custom headers
        response['Access-Control-Expose-Headers'] = 'X-Total-Count, X-Page-Count'
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        
        return response
