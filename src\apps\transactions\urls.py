"""
API URL configuration for Arena Doviz Transactions app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    TransactionTypeViewSet, TransactionViewSet, BalanceEntryViewSet,
    TransactionDocumentViewSet, CommissionRuleViewSet, CommissionTierViewSet
)

app_name = 'transactions'

# API router for transactions endpoints
router = DefaultRouter()
router.register(r'types', TransactionTypeViewSet)
router.register(r'transactions', TransactionViewSet)
router.register(r'balance-entries', BalanceEntryViewSet)
router.register(r'documents', TransactionDocumentViewSet)
router.register(r'commission-rules', CommissionRuleViewSet)
router.register(r'commission-tiers', CommissionTierViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
