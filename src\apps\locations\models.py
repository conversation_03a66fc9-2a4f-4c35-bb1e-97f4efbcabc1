"""
Location models for Arena Doviz Exchange Accounting System.
Handles multi-location operations across Istanbul, Tabriz, Tehran, Dubai, and China.
"""

from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
from apps.core.models import BaseModel
import logging

logger = logging.getLogger(__name__)


class Location(BaseModel):
    """
    Model representing physical locations where exchange operations take place.
    Supports multi-location operations as per business requirements.
    """
    
    name = models.CharField(
        _('Location name'),
        max_length=100,
        help_text=_('Name of the location (e.g., Istanbul, Tabriz)')
    )
    
    code = models.CharField(
        _('Location code'),
        max_length=10,
        unique=True,
        validators=[
            RegexValidator(
                regex=r'^[A-Z]{2,10}$',
                message=_('Location code must be 2-10 uppercase letters')
            )
        ],
        help_text=_('Unique code for the location (e.g., IST, TBZ)')
    )
    
    country = models.CharField(
        _('Country'),
        max_length=100,
        help_text=_('Country where the location is situated')
    )
    
    city = models.CharField(
        _('City'),
        max_length=100,
        help_text=_('City where the location is situated')
    )
    
    address = models.TextField(
        _('Address'),
        blank=True,
        help_text=_('Full address of the location')
    )
    
    phone_number = models.CharField(
        _('Phone number'),
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_('Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.')
            )
        ],
        help_text=_('Contact phone number for the location')
    )
    
    email = models.EmailField(
        _('Email'),
        blank=True,
        help_text=_('Contact email for the location')
    )
    
    timezone = models.CharField(
        _('Timezone'),
        max_length=50,
        default='Asia/Tehran',
        help_text=_('Timezone for the location (e.g., Asia/Tehran, Europe/Istanbul)')
    )
    
    is_active = models.BooleanField(
        _('Is active'),
        default=True,
        help_text=_('Whether this location is currently active')
    )
    
    is_main_office = models.BooleanField(
        _('Is main office'),
        default=False,
        help_text=_('Whether this is the main office location')
    )
    
    manager = models.ForeignKey(
        'accounts.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_locations',
        verbose_name=_('Location manager'),
        help_text=_('User responsible for managing this location')
    )
    
    # Business settings
    commission_rate = models.DecimalField(
        _('Commission rate'),
        max_digits=5,
        decimal_places=4,
        default=0.0100,  # 1%
        help_text=_('Default commission rate for transactions at this location')
    )
    
    min_transaction_amount = models.DecimalField(
        _('Minimum transaction amount'),
        max_digits=15,
        decimal_places=2,
        default=0.00,
        help_text=_('Minimum amount for transactions at this location')
    )
    
    max_transaction_amount = models.DecimalField(
        _('Maximum transaction amount'),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Maximum amount for transactions at this location')
    )
    
    # Operating hours
    opening_time = models.TimeField(
        _('Opening time'),
        null=True,
        blank=True,
        help_text=_('Daily opening time')
    )
    
    closing_time = models.TimeField(
        _('Closing time'),
        null=True,
        blank=True,
        help_text=_('Daily closing time')
    )
    
    # Additional settings
    notes = models.TextField(
        _('Notes'),
        blank=True,
        help_text=_('Additional notes about this location')
    )
    
    sort_order = models.PositiveIntegerField(
        _('Sort order'),
        default=0,
        help_text=_('Order in which locations should be displayed')
    )
    
    class Meta:
        verbose_name = _('Location')
        verbose_name_plural = _('Locations')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_main_office']),
            models.Index(fields=['country', 'city']),
            models.Index(fields=['sort_order']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['is_main_office'],
                condition=models.Q(is_main_office=True),
                name='unique_main_office'
            )
        ]
    
    def __str__(self):
        return f"{self.name} ({self.code})"
    
    def get_display_name(self):
        """Return a display name for the location."""
        return f"{self.name}, {self.country}"
    
    def get_full_address(self):
        """Return the full address including city and country."""
        parts = [self.address, self.city, self.country]
        return ', '.join(filter(None, parts))
    
    def is_operating_now(self):
        """Check if the location is currently operating based on opening hours."""
        if not self.opening_time or not self.closing_time:
            return True  # Assume 24/7 if no hours specified
        
        from django.utils import timezone
        import pytz
        
        try:
            # Get current time in location's timezone
            location_tz = pytz.timezone(self.timezone)
            current_time = timezone.now().astimezone(location_tz).time()
            
            # Check if current time is within operating hours
            if self.opening_time <= self.closing_time:
                # Same day operation
                return self.opening_time <= current_time <= self.closing_time
            else:
                # Overnight operation (crosses midnight)
                return current_time >= self.opening_time or current_time <= self.closing_time
                
        except Exception as e:
            logger.error(f"Error checking operating hours for location {self.code}: {e}")
            return True  # Default to operating if there's an error
    
    def get_current_time(self):
        """Get the current time in the location's timezone."""
        from django.utils import timezone
        import pytz
        
        try:
            location_tz = pytz.timezone(self.timezone)
            return timezone.now().astimezone(location_tz)
        except Exception as e:
            logger.error(f"Error getting current time for location {self.code}: {e}")
            return timezone.now()
    
    def get_balances_summary(self):
        """Get a summary of all currency balances at this location."""
        from apps.transactions.models import BalanceEntry
        
        try:
            balances = BalanceEntry.objects.filter(
                location=self,
                is_deleted=False
            ).values('currency__code').annotate(
                total_balance=models.Sum('amount')
            ).order_by('currency__code')
            
            return {balance['currency__code']: balance['total_balance'] for balance in balances}
            
        except Exception as e:
            logger.error(f"Error getting balance summary for location {self.code}: {e}")
            return {}
    
    def get_active_users_count(self):
        """Get the number of active users assigned to this location."""
        return self.users.filter(is_active=True, is_deleted=False).count()
    
    def save(self, *args, **kwargs):
        """
        Override save to handle business logic.
        """
        # Ensure only one main office
        if self.is_main_office:
            Location.objects.filter(is_main_office=True).exclude(pk=self.pk).update(is_main_office=False)
        
        # Auto-generate code if not provided
        if not self.code and self.name:
            self.code = self.name[:3].upper()
        
        super().save(*args, **kwargs)
        
        logger.info(f"Location saved: {self.name} ({self.code})")
    
    @classmethod
    def get_main_office(cls):
        """Get the main office location."""
        try:
            return cls.objects.get(is_main_office=True, is_active=True, is_deleted=False)
        except cls.DoesNotExist:
            logger.warning("No main office location found")
            return None
        except cls.MultipleObjectsReturned:
            logger.error("Multiple main office locations found")
            return cls.objects.filter(is_main_office=True, is_active=True, is_deleted=False).first()
    
    @classmethod
    def get_active_locations(cls):
        """Get all active locations."""
        return cls.objects.filter(is_active=True, is_deleted=False).order_by('sort_order', 'name')


class LocationSettings(BaseModel):
    """
    Model for storing location-specific settings and configurations.
    """
    
    location = models.OneToOneField(
        Location,
        on_delete=models.CASCADE,
        related_name='settings',
        verbose_name=_('Location')
    )
    
    # WhatsApp integration settings
    whatsapp_enabled = models.BooleanField(
        _('WhatsApp enabled'),
        default=True,
        help_text=_('Whether WhatsApp integration is enabled for this location')
    )
    
    whatsapp_group_id = models.CharField(
        _('WhatsApp group ID'),
        max_length=100,
        blank=True,
        help_text=_('WhatsApp group ID for notifications')
    )
    
    # Notification settings
    email_notifications = models.BooleanField(
        _('Email notifications'),
        default=True,
        help_text=_('Whether to send email notifications')
    )
    
    sms_notifications = models.BooleanField(
        _('SMS notifications'),
        default=False,
        help_text=_('Whether to send SMS notifications')
    )
    
    # Transaction settings
    require_approval_above = models.DecimalField(
        _('Require approval above'),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Transaction amount above which approval is required')
    )
    
    auto_approve_below = models.DecimalField(
        _('Auto approve below'),
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Transaction amount below which auto-approval is enabled')
    )
    
    # Security settings
    ip_whitelist = models.TextField(
        _('IP whitelist'),
        blank=True,
        help_text=_('Comma-separated list of allowed IP addresses')
    )
    
    session_timeout = models.PositiveIntegerField(
        _('Session timeout'),
        default=3600,  # 1 hour
        help_text=_('Session timeout in seconds')
    )
    
    # Backup settings
    backup_enabled = models.BooleanField(
        _('Backup enabled'),
        default=True,
        help_text=_('Whether automatic backups are enabled')
    )
    
    backup_frequency = models.CharField(
        _('Backup frequency'),
        max_length=20,
        choices=[
            ('daily', _('Daily')),
            ('weekly', _('Weekly')),
            ('monthly', _('Monthly')),
        ],
        default='daily',
        help_text=_('How often to perform backups')
    )
    
    # Additional settings stored as JSON
    additional_settings = models.JSONField(
        _('Additional settings'),
        default=dict,
        blank=True,
        help_text=_('Additional location-specific settings')
    )
    
    class Meta:
        verbose_name = _('Location Settings')
        verbose_name_plural = _('Location Settings')
    
    def __str__(self):
        return f"Settings for {self.location.name}"
    
    def get_allowed_ips(self):
        """Get list of allowed IP addresses."""
        if not self.ip_whitelist:
            return []
        return [ip.strip() for ip in self.ip_whitelist.split(',') if ip.strip()]
    
    def is_ip_allowed(self, ip_address):
        """Check if an IP address is allowed."""
        allowed_ips = self.get_allowed_ips()
        if not allowed_ips:
            return True  # No restrictions if whitelist is empty
        return ip_address in allowed_ips
    
    def get_setting(self, key, default=None):
        """Get a custom setting value."""
        return self.additional_settings.get(key, default)
    
    def set_setting(self, key, value):
        """Set a custom setting value."""
        self.additional_settings[key] = value
        self.save(update_fields=['additional_settings'])
        logger.debug(f"Setting updated for location {self.location.code}: {key} = {value}")
